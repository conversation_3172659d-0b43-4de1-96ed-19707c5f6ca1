from qdrant_client import QdrantClient
from qdrant_client.http.models import PointStruct

# C<PERSON>u hình kết nối Qdrant
qdrant_host = "localhost"  # hoặc domain nếu chạy từ xa
qdrant_port = 6333                # cổng mặc định
collection_name = "collection_ssg_detail"  # thay tên collection bạn đang dùng

# Khởi tạo client
client = QdrantClient(host=qdrant_host, port=qdrant_port)

# Truy vấn 10 điểm đầu tiên để kiểm tra
response = client.scroll(
    collection_name=collection_name,
    limit=10,
    with_vectors=True,
)

print("📦 Kiểm tra dữ liệu trong collection:", collection_name)
for idx, point in enumerate(response[0]):
    print(f"\n▶️ Point {idx + 1}:")
    print("ID:", point.id)
    print("Vector:", point.vector)  # In 5 số đầu vector
    print("Payload:", point.payload)
