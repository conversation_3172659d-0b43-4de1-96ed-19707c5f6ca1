# BGE-M3 Migration Guide

## Tổng quan
Tài liệu này mô tả việc chuy<PERSON>n đ<PERSON>i từ Google Gemini embedding model sang BGE-M3 model trong file `app/utils/qdrant_simple.py`.

## Nh<PERSON>ng thay đổi chính

### 1. Thay đổi Dependencies
**Trước:**
```python
import google.generativeai as genai
```

**Sau:**
```python
from sentence_transformers import SentenceTransformer
```

### 2. Cập nhật Requirements
Đã thêm vào `requirements.txt`:
```
sentence-transformers>=2.7.0
```

### 3. Thay đổi Model Loading

**Trước (Gemini):**
```python
def get_embedding_model_simple():
    genai.configure(api_key=settings.API_KEY)
    
    def embed_query(text: str):
        response = genai.embed_content(
            model="models/embedding-001",
            content=text,
            task_type="retrieval_query"
        )
        return response["embedding"]
    
    return type('GeminiEmbedding', (), {'embed_query': embed_query})()
```

**Sau (BGE-M3):**
```python
def get_embedding_model_simple():
    global _embedding_model
    if _embedding_model is None:
        _embedding_model = SentenceTransformer('BAAI/bge-m3')
    
    class BGEM3Embedding:
        def embed_query(self, text: str):
            embedding = _embedding_model.encode(text, convert_to_tensor=False)
            return embedding.tolist()
    
    return BGEM3Embedding()
```

### 4. Thay đổi Vector Dimensions
- **Gemini:** 768 dimensions
- **BGE-M3:** 1024 dimensions

### 5. Cập nhật Health Check
Đã thêm kiểm tra dimension mismatch và cảnh báo khi collection không tương thích:

```python
# Warning if dimension mismatch
if vector_size != 1024:
    logger.warning(f"Dimension mismatch: Collection expects {vector_size} but BGE-M3 produces 1024 dimensions")
    logger.warning("You may need to recreate the collection with correct dimensions or use a different model")
```

## Ưu điểm của BGE-M3

### 1. Multi-functionality
- **Dense retrieval:** Embedding truyền thống
- **Sparse retrieval:** Tương tự BM25
- **Multi-vector retrieval:** ColBERT style

### 2. Multi-linguality
- Hỗ trợ hơn 100 ngôn ngữ
- Hiệu suất tốt cho tiếng Việt

### 3. Multi-granularity
- Xử lý được văn bản từ câu ngắn đến tài liệu dài (8192 tokens)

### 4. Performance
- Hiệu suất cao trên nhiều benchmark
- Không cần API key (chạy local)

## Vấn đề cần lưu ý

### 1. Dimension Mismatch
Collection Qdrant hiện tại có thể đang sử dụng dimensions khác (4096). Cần:
- Tạo lại collection với 1024 dimensions, hoặc
- Sử dụng model khác có cùng dimensions

### 2. Model Size
BGE-M3 model khá lớn (~2.3GB), cần:
- Đủ RAM để load model
- Thời gian download lần đầu

### 3. Dependencies
Cần cài đặt thêm:
- `sentence-transformers`
- `torch` (nếu chưa có)

## Cách sử dụng

### 1. Test Model
```bash
python test_bge_m3.py
```

### 2. Sử dụng trong code
```python
from app.utils.qdrant_simple import get_embedding_model_simple

# Load model
model = get_embedding_model_simple()

# Tạo embedding
embedding = model.embed_query("Your text here")
print(f"Dimension: {len(embedding)}")  # Should be 1024
```

### 3. Search với BGE-M3
```python
from app.utils.qdrant_simple import search_qdrant_simple

# Search (sẽ fail nếu dimension không match)
results = search_qdrant_simple("Your query here")
```

## Khắc phục sự cố

### 1. Import Error
```bash
pip install sentence-transformers>=2.7.0
```

### 2. Dimension Mismatch
Cần tạo lại Qdrant collection với 1024 dimensions:
```python
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams

client = QdrantClient(host="localhost", port=6333)
client.recreate_collection(
    collection_name="your_collection",
    vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
)
```

### 3. Memory Issues
- Đảm bảo có đủ RAM (ít nhất 4GB free)
- Có thể sử dụng `use_fp16=True` để giảm memory

## Kết luận
BGE-M3 là một upgrade đáng kể so với Gemini embedding:
- Không cần API key
- Hiệu suất tốt hơn
- Hỗ trợ đa ngôn ngữ tốt hơn
- Nhiều chức năng hơn

Tuy nhiên cần chú ý về dimension mismatch và yêu cầu tài nguyên.
