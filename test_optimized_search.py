#!/usr/bin/env python3
"""
Test script for optimized search and answer synthesis
"""
import sys
import os
import json

# Add the current directory to Python path
sys.path.append('.')

from app.utils.elasticsearch_optimized import build_es_query, search_elasticsearch_simple
from app.utils.qdrant_simple import search_qdrant_simple
from app.engine.chains.answer_synthesis_chain import synthesize_answer, deduplicate_and_rank_content
from app.config.index import settings

def test_elasticsearch_query():
    """Test improved Elasticsearch query building"""
    print("=" * 60)
    print("Testing Elasticsearch Query Optimization")
    print("=" * 60)
    
    test_queries = [
        "sao mai solution",
        "dịch vụ phần mềm",
        "hệ thống quản lý",
        "artificial intelligence AI"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        es_query = build_es_query(query)
        print("Generated ES Query:")
        print(json.dumps(es_query, indent=2, ensure_ascii=False))
        print("-" * 40)

def test_search_integration():
    """Test integrated search functionality"""
    print("\n" + "=" * 60)
    print("Testing Integrated Search")
    print("=" * 60)
    
    test_question = "Sao Mai Solution Group cung cấp dịch vụ gì?"
    
    print(f"Question: {test_question}")
    
    # Test Elasticsearch
    print("\n1. Testing Elasticsearch Search...")
    try:
        es_results = search_elasticsearch_simple(
            test_question, 
            settings.ES_INDEX,
            settings.ES_URL
        )
        print(f"ES Results: {len(es_results)} documents found")
        if es_results:
            print(f"First result score: {es_results[0].get('_score', 'N/A')}")
    except Exception as e:
        print(f"ES Search Error: {e}")
        es_results = []
    
    # Test Qdrant
    print("\n2. Testing Qdrant Search...")
    try:
        qdrant_results = search_qdrant_simple(test_question)
        print(f"Qdrant Results: {len(qdrant_results)} documents found")
    except Exception as e:
        print(f"Qdrant Search Error: {e}")
        qdrant_results = []
    
    # Test Deduplication
    print("\n3. Testing Deduplication...")
    try:
        if es_results or qdrant_results:
            filtered_es, filtered_qdrant = deduplicate_and_rank_content(es_results, qdrant_results)
            print(f"After deduplication:")
            print(f"  ES: {len(es_results)} -> {len(filtered_es)}")
            print(f"  Qdrant: {len(qdrant_results)} -> {len(filtered_qdrant)}")
        else:
            print("No results to deduplicate")
    except Exception as e:
        print(f"Deduplication Error: {e}")
    
    # Test Answer Synthesis
    print("\n4. Testing Answer Synthesis...")
    try:
        answer = synthesize_answer(test_question, es_results, qdrant_results)
        print("Generated Answer:")
        print("-" * 40)
        print(answer)
        print("-" * 40)
    except Exception as e:
        print(f"Answer Synthesis Error: {e}")

def test_query_variations():
    """Test different types of queries"""
    print("\n" + "=" * 60)
    print("Testing Query Variations")
    print("=" * 60)
    
    query_types = [
        ("Keyword Query", "phần mềm quản lý"),
        ("Phrase Query", "Sao Mai Solution Group"),
        ("Mixed Query", "AI artificial intelligence machine learning"),
        ("Vietnamese Query", "dịch vụ công nghệ thông tin"),
        ("Technical Query", "API REST microservices")
    ]
    
    for query_type, query in query_types:
        print(f"\n{query_type}: '{query}'")
        
        # Test ES query structure
        es_query = build_es_query(query)
        print(f"ES Query complexity: {len(es_query['query']['bool']['should'])} clauses")
        print(f"Min score threshold: {es_query.get('min_score', 'None')}")
        print(f"Result size limit: {es_query['size']}")

def main():
    """Main test function"""
    print("🚀 Optimized Search & Answer Synthesis Test")
    print("=" * 60)
    
    # Test 1: Query building
    test_elasticsearch_query()
    
    # Test 2: Query variations
    test_query_variations()
    
    # Test 3: Integrated search (may fail due to connection issues)
    test_search_integration()
    
    print("\n" + "=" * 60)
    print("✅ Test Summary")
    print("=" * 60)
    print("Key Improvements Implemented:")
    print("• Enhanced Elasticsearch query with boosting and phrase matching")
    print("• Improved Qdrant search with score thresholds")
    print("• Content deduplication and ranking")
    print("• Better answer synthesis with Vietnamese prompts")
    print("• Intelligent text truncation")
    print("• Quality assessment for search results")
    
    print("\nExpected Benefits:")
    print("• More relevant search results")
    print("• Reduced duplicate content")
    print("• Better structured answers")
    print("• Improved Vietnamese language support")
    print("• Higher quality content filtering")

if __name__ == "__main__":
    main()
