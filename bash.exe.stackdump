Stack trace:
Frame         Function      Args
0007FFFFA930  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9830) msys-2.0.dll+0x2118E
0007FFFFA930  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA930  0002100469F2 (00021028DF99, 0007FFFFA7E8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA930  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA930  00021006A545 (0007FFFFA940, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA940, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC316E0000 ntdll.dll
7FFC30B80000 KERNEL32.DLL
7FFC2EA40000 KERNELBASE.dll
7FFC30E00000 USER32.dll
7FFC2EA10000 win32u.dll
7FFC31060000 GDI32.dll
000210040000 msys-2.0.dll
7FFC2EFB0000 gdi32full.dll
7FFC2F270000 msvcp_win.dll
7FFC2E8C0000 ucrtbase.dll
7FFC2F480000 advapi32.dll
7FFC312F0000 msvcrt.dll
7FFC31570000 sechost.dll
7FFC30130000 RPCRT4.dll
7FFC2DE10000 CRYPTBASE.DLL
7FFC2F3E0000 bcryptPrimitives.dll
7FFC30DA0000 IMM32.DLL
