"""
Performance monitoring middleware
"""
import time
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable
from app.core.cache_manager import cache_manager

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware để monitor performance của API"""
    
    def __init__(self, app, slow_request_threshold: float = 1.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
        self.request_count = 0
        self.total_time = 0.0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Start timing
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Update stats
        self.request_count += 1
        self.total_time += duration
        
        # Log slow requests
        if duration > self.slow_request_threshold:
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path} "
                f"took {duration:.2f}s"
            )
        
        # Add performance headers
        response.headers["X-Process-Time"] = str(duration)
        response.headers["X-Request-Count"] = str(self.request_count)
        
        # Log request info
        logger.info(
            f"{request.method} {request.url.path} "
            f"completed in {duration:.3f}s "
            f"with status {response.status_code}"
        )
        
        return response
    
    def get_stats(self) -> dict:
        """Lấy thống kê performance"""
        avg_time = self.total_time / self.request_count if self.request_count > 0 else 0
        return {
            "total_requests": self.request_count,
            "total_time": round(self.total_time, 3),
            "average_time": round(avg_time, 3),
            "cache_stats": cache_manager.get_all_stats()
        }


# Global instance
performance_middleware = PerformanceMiddleware(None)
