from app.engine.chains.classification_llm_chain import classification_llm_chain
from app.utils.index import set_global_state
import logging

logger = logging.getLogger(__name__)


def classify_question(state):
    """
    Simple question classification without caching for now
    """
    question = state["question"]

    try:
        # Perform classification
        logger.info(f"Classifying question: {question[:50]}...")
        classification = classification_llm_chain.invoke({"question": question})

        # Clean and extract classification - handle various formats
        classification_clean = classification.strip().lower()

        # Extract the actual classification word if it's in a formatted response
        valid_classifications = ["keyword", "semantic", "hybrid"]
        extracted_classification = None

        for valid_class in valid_classifications:
            if valid_class in classification_clean:
                extracted_classification = valid_class
                break

        if extracted_classification:
            classification_clean = extracted_classification
        elif classification_clean not in valid_classifications:
            logger.warning(f"Invalid classification '{classification_clean}', defaulting to 'hybrid'")
            classification_clean = "hybrid"

        # Set state
        state["classification"] = classification_clean
        set_global_state("classification", classification_clean)

        logger.info(f"Question classified as: {classification_clean}")

    except Exception as e:
        logger.error(f"Classification failed: {e}")
        # Default to hybrid if classification fails
        state["classification"] = "hybrid"
        set_global_state("classification", "hybrid")

    return state
