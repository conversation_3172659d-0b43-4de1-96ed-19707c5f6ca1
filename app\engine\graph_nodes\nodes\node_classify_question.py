from app.engine.chains.classification_llm_chain import classification_llm_chain
from app.utils.index import set_global_state
import logging

logger = logging.getLogger(__name__)


def classify_question(state):
    """
    Simple question classification without caching for now
    """
    question = state["question"]

    try:
        # Perform classification
        logger.info(f"Classifying question: {question[:50]}...")
        classification = classification_llm_chain.invoke({"question": question})
        classification_clean = classification.strip().lower()

        # Set state
        state["classification"] = classification_clean
        set_global_state("classification", classification)

        logger.info(f"Question classified as: {classification_clean}")

    except Exception as e:
        logger.error(f"Classification failed: {e}")
        # Default to hybrid if classification fails
        state["classification"] = "hybrid"
        set_global_state("classification", "hybrid")

    return state
