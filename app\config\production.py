"""
Production configuration settings
"""
from pydantic_settings import BaseSettings
from typing import Optional


class ProductionSettings(BaseSettings):
    """Production-specific settings"""
    
    # Performance settings
    WORKERS: int = 4
    MAX_CONNECTIONS: int = 100
    KEEP_ALIVE: int = 2
    
    # Cache settings
    CACHE_TTL_SEARCH: int = 3600  # 1 hour
    CACHE_TTL_AI: int = 1800      # 30 minutes
    CACHE_TTL_EMBEDDING: int = 86400  # 24 hours
    CACHE_TTL_CLASSIFICATION: int = 21600  # 6 hours
    
    # Database settings
    DB_POOL_SIZE: int = 50
    DB_MIN_POOL_SIZE: int = 10
    DB_MAX_IDLE_TIME: int = 30000
    DB_SERVER_SELECTION_TIMEOUT: int = 5000
    DB_CONNECT_TIMEOUT: int = 10000
    DB_SOCKET_TIMEOUT: int = 20000
    
    # Vector store settings
    QDRANT_TIMEOUT: int = 30
    ES_MAX_RETRIES: int = 3
    ES_TIMEOUT: int = 30
    ES_MAX_TIMEOUT: int = 60
    
    # LLM settings
    LLM_TEMPERATURE: float = 0.1
    LLM_NUM_PREDICT: int = 512
    LLM_TOP_K: int = 10
    LLM_TOP_P: float = 0.9
    
    # Search settings
    SEARCH_K: int = 2
    SEARCH_FETCH_K: int = 20
    SEARCH_LAMBDA_MULT: float = 0.7
    
    # Monitoring settings
    SLOW_REQUEST_THRESHOLD: float = 1.0
    LOG_LEVEL: str = "INFO"
    
    # Security settings
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["*"]
    CORS_ALLOW_HEADERS: list = ["*"]
    
    class Config:
        env_file = ".env.production"
        env_prefix = "PROD_"


class DevelopmentSettings(BaseSettings):
    """Development-specific settings"""
    
    # Performance settings (lower for development)
    WORKERS: int = 1
    MAX_CONNECTIONS: int = 20
    KEEP_ALIVE: int = 2
    
    # Cache settings (shorter TTL for development)
    CACHE_TTL_SEARCH: int = 300   # 5 minutes
    CACHE_TTL_AI: int = 300       # 5 minutes
    CACHE_TTL_EMBEDDING: int = 3600  # 1 hour
    CACHE_TTL_CLASSIFICATION: int = 1800  # 30 minutes
    
    # Database settings (smaller pool for development)
    DB_POOL_SIZE: int = 10
    DB_MIN_POOL_SIZE: int = 2
    DB_MAX_IDLE_TIME: int = 30000
    DB_SERVER_SELECTION_TIMEOUT: int = 5000
    DB_CONNECT_TIMEOUT: int = 10000
    DB_SOCKET_TIMEOUT: int = 20000
    
    # Vector store settings
    QDRANT_TIMEOUT: int = 30
    ES_MAX_RETRIES: int = 2
    ES_TIMEOUT: int = 15
    ES_MAX_TIMEOUT: int = 30
    
    # LLM settings (more creative for development)
    LLM_TEMPERATURE: float = 0.3
    LLM_NUM_PREDICT: int = 256
    LLM_TOP_K: int = 10
    LLM_TOP_P: float = 0.9
    
    # Search settings
    SEARCH_K: int = 2
    SEARCH_FETCH_K: int = 10
    SEARCH_LAMBDA_MULT: float = 0.7
    
    # Monitoring settings
    SLOW_REQUEST_THRESHOLD: float = 2.0
    LOG_LEVEL: str = "DEBUG"
    
    # Security settings
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["*"]
    CORS_ALLOW_HEADERS: list = ["*"]
    
    class Config:
        env_file = ".env.development"
        env_prefix = "DEV_"


def get_settings(environment: str = "development"):
    """Get settings based on environment"""
    if environment.lower() == "production":
        return ProductionSettings()
    else:
        return DevelopmentSettings()


# Default settings
app_settings = get_settings()
