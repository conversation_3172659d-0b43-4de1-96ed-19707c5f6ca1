from app.utils.index import set_global_state
from app.utils.qdrant_simple import search_qdrant_simple
import logging

logger = logging.getLogger(__name__)


def node_retrieve(state):
    """
    Simple node retrieve without async dependencies
    """
    classification = state.get("classification")
    if classification in ["semantic", "hybrid"]:
        question = state.get("question", "")

        try:
            # Perform simple sync search
            docs = search_qdrant_simple(question)

            # Set global state and return
            set_global_state("qdrant_data", docs)
            state["qdrant_data"] = docs

            logger.info(f"Qdrant search completed, found {len(docs)} documents")

        except Exception as e:
            logger.error(f"Qdrant search failed: {e}")
            # Set empty result instead of raising
            state["qdrant_data"] = []
            set_global_state("qdrant_data", [])

    return state
