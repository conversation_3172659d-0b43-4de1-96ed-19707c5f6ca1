from app.utils.index import get_global_state

def get_es_keywords_and_texts():
    global_state = get_global_state()
    # print("[ES TOOL] Đọc global_state id:", id(global_state))
    # print(f"[ES TOOL] global_state: {global_state}")
    try:
        # Check if key exists
        if "es_data" not in global_state:
            raise ValueError("Key 'es_data' does not exist in global_state")

        docs_es = global_state["es_data"]
        # print("[ES TOOL] docs_es: ", docs_es)

        result_text = "\n\n".join([
            f"Keywords: {doc['_source'].get('keyword', '')}\nText: {doc['_source'].get('text', '')}"
            for doc in docs_es
        ])
        return result_text
    except Exception as e:
        return f"[get_es_keywords_and_texts] Error: {e}"

def es_tool_wrapper(query) -> str:
    """
    Search for documents using Elasticsearch based on the provided keyword string.
    The input should be a single keyword or phrase, e.g., 'API Gateway'.
    """
    try:
        # print(f"[ES TOOL] Elasticsearch tool received input: {query} type: {type(query)}")
        
        # Parse action_input nếu là object
        if isinstance(query, dict) and "value" in query:
            query_str = query["value"]
        else:
            query_str = query

        # print(f"[ES TOOL] Parsed Elasticsearch query: {query_str}")
        return get_es_keywords_and_texts()
    except Exception as e:
        return f"Error processing Elasticsearch data: {e}"