#!/usr/bin/env python3
"""
Test script for Vietnamese prompts
"""
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

from app.engine.chains.classification_llm_chain import classification_llm_chain
from app.engine.chains.keywords_llm_chain import keywords_llm_chain
from app.engine.chains.answer_synthesis_chain import synthesize_answer
from app.engine.chains.llm_chain import llm_chain

def test_classification_prompt():
    """Test Vietnamese classification prompt"""
    print("=" * 60)
    print("Testing Vietnamese Classification Prompt")
    print("=" * 60)
    
    test_questions = [
        "Sao Mai Solution Group cung cấp dịch vụ gì?",
        "<PERSON><PERSON><PERSON> thế nào để reset mật khẩu Windows?",
        "Tại sao nên sử dụng kiến trúc microservices?",
        "Chính sách nghỉ phép của công ty như thế nào?",
        "Lệnh git để tạo branch mới là gì?"
    ]
    
    for question in test_questions:
        print(f"\nCâu hỏi: '{question}'")
        try:
            classification = classification_llm_chain.invoke({"question": question})
            print(f"Phân loại: {classification.strip()}")
        except Exception as e:
            print(f"Lỗi: {e}")
        print("-" * 40)

def test_keywords_extraction():
    """Test Vietnamese keywords extraction"""
    print("\n" + "=" * 60)
    print("Testing Vietnamese Keywords Extraction")
    print("=" * 60)
    
    test_questions = [
        "Sao Mai Solution Group có những sản phẩm AI nào?",
        "Chính sách lương thưởng của công ty ra sao?",
        "Hướng dẫn sử dụng API Portal như thế nào?",
        "Quy trình onboarding nhân viên mới",
        "Tài liệu về hệ thống quản lý dự án"
    ]
    
    for question in test_questions:
        print(f"\nCâu hỏi: '{question}'")
        try:
            keywords = keywords_llm_chain.invoke({"question": question})
            print(f"Từ khóa: {keywords.strip()}")
        except Exception as e:
            print(f"Lỗi: {e}")
        print("-" * 40)

def test_answer_synthesis():
    """Test Vietnamese answer synthesis"""
    print("\n" + "=" * 60)
    print("Testing Vietnamese Answer Synthesis")
    print("=" * 60)
    
    # Mock data for testing
    mock_es_data = [
        {
            "_score": 0.85,
            "_source": {
                "keyword": "Sao Mai Solution Group, dịch vụ phần mềm",
                "text": "Sao Mai Solution Group là tập đoàn công nghệ hàng đầu Việt Nam, chuyên cung cấp các giải pháp phần mềm doanh nghiệp, phát triển ứng dụng di động, và dịch vụ tư vấn công nghệ thông tin.",
                "metadata": "Giới thiệu công ty - Trang chủ"
            }
        }
    ]
    
    mock_qdrant_data = [
        type('Document', (), {
            'page_content': 'Tập đoàn Sao Mai Solution Group được thành lập từ năm 2010, với đội ngũ hơn 500 chuyên gia IT, chúng tôi đã triển khai thành công hơn 1000 dự án cho các doanh nghiệp lớn.',
            'metadata': {'source': 'Hồ sơ năng lực công ty'}
        })()
    ]
    
    test_question = "Sao Mai Solution Group cung cấp những dịch vụ gì?"
    
    print(f"Câu hỏi: {test_question}")
    print("\nDữ liệu mẫu:")
    print("- ES: 1 tài liệu")
    print("- Qdrant: 1 tài liệu")
    
    try:
        answer = synthesize_answer(test_question, mock_es_data, mock_qdrant_data)
        print("\nCâu trả lời được tổng hợp:")
        print("-" * 40)
        print(answer)
        print("-" * 40)
    except Exception as e:
        print(f"Lỗi: {e}")

def test_general_llm():
    """Test general Vietnamese LLM chain"""
    print("\n" + "=" * 60)
    print("Testing General Vietnamese LLM Chain")
    print("=" * 60)
    
    test_question = "Xin chào, bạn có thể giúp tôi không?"
    
    print(f"Câu hỏi: {test_question}")
    try:
        response = llm_chain.invoke({"question": test_question})
        print(f"Phản hồi: {response}")
    except Exception as e:
        print(f"Lỗi: {e}")

def test_prompt_language_consistency():
    """Test language consistency across prompts"""
    print("\n" + "=" * 60)
    print("Testing Language Consistency")
    print("=" * 60)
    
    # Test with mixed language input
    mixed_questions = [
        "What is Sao Mai Solution Group?",  # English
        "Sao Mai Solution Group là gì?",    # Vietnamese
        "AI Portal 사용법",                  # Korean mixed
        "Comment utiliser l'API?",          # French
    ]
    
    for question in mixed_questions:
        print(f"\nCâu hỏi ({detect_language(question)}): '{question}'")
        
        # Test classification
        try:
            classification = classification_llm_chain.invoke({"question": question})
            print(f"Phân loại: {classification.strip()}")
        except Exception as e:
            print(f"Lỗi phân loại: {e}")
        
        # Test keywords
        try:
            keywords = keywords_llm_chain.invoke({"question": question})
            print(f"Từ khóa: {keywords.strip()}")
        except Exception as e:
            print(f"Lỗi từ khóa: {e}")
        
        print("-" * 40)

def detect_language(text):
    """Simple language detection"""
    if any(ord(char) > 127 for char in text):
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return "Chinese/Japanese/Korean"
        elif any('\u0080' <= char <= '\u024f' for char in text):
            return "European"
        else:
            return "Vietnamese/Other"
    else:
        return "English"

def main():
    """Main test function"""
    print("🇻🇳 Vietnamese Prompts Test Suite")
    print("=" * 60)
    
    # Test 1: Classification
    test_classification_prompt()
    
    # Test 2: Keywords extraction
    test_keywords_extraction()
    
    # Test 3: Answer synthesis
    test_answer_synthesis()
    
    # Test 4: General LLM
    test_general_llm()
    
    # Test 5: Language consistency
    test_prompt_language_consistency()
    
    print("\n" + "=" * 60)
    print("✅ Test Summary")
    print("=" * 60)
    print("Prompts đã được chuyển đổi sang tiếng Việt:")
    print("• Classification Prompt: ✅ Hoàn thành")
    print("• Keywords Extraction: ✅ Hoàn thành") 
    print("• Answer Synthesis: ✅ Hoàn thành")
    print("• Tool Descriptions: ✅ Hoàn thành")
    print("• Response Node Prompt: ✅ Hoàn thành")
    print("• General LLM Chain: ✅ Đã có sẵn")
    
    print("\nLợi ích mong đợi:")
    print("• Hiểu ngữ cảnh tiếng Việt tự nhiên hơn")
    print("• Câu trả lời có tone phù hợp văn hóa Việt")
    print("• Giảm thiểu sai lệch do 'translation thinking'")
    print("• Cải thiện chất lượng với domain-specific terms")

if __name__ == "__main__":
    main()
