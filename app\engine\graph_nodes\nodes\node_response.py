from app.engine.react.agents.summarize_agent import summarize_answer_agent
from app.utils.index import  get_global_state
from app.engine.chains.answer_synthesis_chain import synthesize_answer, format_no_data_response
import logging

logger = logging.getLogger(__name__)



def node_response(state):
    question = state.get("question", "")
    classification = state.get("classification", "")

    print(f"[DEBUG] Câu hỏi: {question}, Phân loại: {classification}")

    # Try improved synthesis approach first
    try:
        return node_response_with_synthesis(state)
    except Exception as e:
        logger.error(f"Synthesis approach failed: {e}")
        # Fallback to original approach
        return node_response_original(state)


def node_response_with_synthesis(state):
    """
    Improved response using synthesis chain
    """
    question = state.get("question", "")

    # Get data from global state
    global_state = get_global_state()
    es_data = global_state.get("es_data", [])
    qdrant_data = global_state.get("qdrant_data", [])

    logger.info(f"Synthesizing answer for: {question}")
    logger.info(f"ES data count: {len(es_data)}, Qdrant data count: {len(qdrant_data)}")

    # Check if we have any data
    if not es_data and not qdrant_data:
        answer = format_no_data_response(question)
    else:
        answer = synthesize_answer(question, es_data, qdrant_data)

    state["answer"] = answer
    return state


def node_response_original(state):
    """
    Original response method as fallback
    """
    question = state.get("question", "")
    classification = state.get("classification", "")
    prompt = f"""
    Bạn là trợ lý AI chuyên về cơ sở tri thức nội bộ của Tập đoàn Sao Mai Solution Group.

    QUY TẮC QUAN TRỌNG:
    - Bạn PHẢI sử dụng các công cụ tìm kiếm để tìm thông tin liên quan trước khi trả lời
    - Dựa trên phân loại:
        - "keyword": chỉ sử dụng **Elasticsearch** để khớp từ khóa chính xác
        - "semantic": chỉ sử dụng **Qdrant** để tìm kiếm tương đồng ngữ nghĩa
        - "hybrid": sử dụng **cả hai** công cụ - bắt đầu với Elasticsearch, sau đó Qdrant
    - Mỗi công cụ chỉ có thể được gọi **một lần duy nhất**
    - Khi sử dụng Elasticsearch, sử dụng **từ khóa đã trích xuất** làm Action Input
    - Khi sử dụng Qdrant, sử dụng **câu hỏi gốc** làm Action Input
    - Bạn PHẢI dựa câu trả lời trên dữ liệu được truy xuất từ các công cụ
    - Nếu không tìm thấy dữ liệu liên quan, hãy nói rõ rằng thông tin không có sẵn trong cơ sở tri thức
    - Câu trả lời cuối cùng **phải bằng tiếng Việt** và bao gồm chi tiết cụ thể từ các tài liệu được truy xuất
    - Chỉ sử dụng **2 định dạng** bên dưới. Không tự tạo định dạng mới.

    ---
    Định dạng 1 (sử dụng công cụ):
    Thought: [giải thích tại sao bạn cần sử dụng công cụ]
    Action: [Elasticsearch hoặc Qdrant]
    Action Input: [từ khóa hoặc câu hỏi gốc]

    Định dạng 2 (sau khi có đủ dữ liệu):
    Thought: [giải thích tại sao bạn có thể trả lời]
    Final Answer: [câu trả lời ngắn gọn, tập trung bằng tiếng Việt]

    ---

    Bắt đầu!

    Câu hỏi: {question}
    Phân loại: {classification}
    """

    # ✅ invoke đúng định dạng agent yêu cầu
    response = summarize_answer_agent.invoke({"input": prompt})
    
    # response = safe_run_agent(node_response_agent, question)
    print(f"response: {response}")

    # Nếu agent trả về string, không có `.get('output')`
    if isinstance(response, str):
        state["answer"] = response
    elif isinstance(response, dict) and "output" in response:
        state["answer"] = response["output"]
    else:
        state["answer"] = "[Error] Unable to get result from agent"

    return state
