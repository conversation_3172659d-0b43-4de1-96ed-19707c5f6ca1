from app.engine.react.agents.summarize_agent import summarize_answer_agent
from app.utils.index import  get_global_state
from app.engine.chains.answer_synthesis_chain import synthesize_answer, format_no_data_response
import logging

logger = logging.getLogger(__name__)



def node_response(state):
    question = state.get("question", "")
    classification = state.get("classification", "")

    print(f"[DEBUG] Câu hỏi: {question}, Phân loại: {classification}")

    # Try improved synthesis approach first
    try:
        return node_response_with_synthesis(state)
    except Exception as e:
        logger.error(f"Synthesis approach failed: {e}")
        # Fallback to original approach
        return node_response_original(state)


def node_response_with_synthesis(state):
    """
    Improved response using synthesis chain
    """
    question = state.get("question", "")

    # Get data from global state
    global_state = get_global_state()
    es_data = global_state.get("es_data", [])
    qdrant_data = global_state.get("qdrant_data", [])

    logger.info(f"Synthesizing answer for: {question}")
    logger.info(f"ES data count: {len(es_data)}, Qdrant data count: {len(qdrant_data)}")

    # Check if we have any data
    if not es_data and not qdrant_data:
        answer = format_no_data_response(question)
    else:
        answer = synthesize_answer(question, es_data, qdrant_data)

    state["answer"] = answer
    return state


def node_response_original(state):
    """
    Original response method as fallback
    """
    question = state.get("question", "")
    classification = state.get("classification", "")
    prompt = f"""
    You are an AI assistant specializing in Sao Mai Solution Group's internal knowledge base.

    IMPORTANT RULES:
    - You MUST use the search tools to find relevant information before answering
    - Based on classification:
        - "keyword": use **Elasticsearch** only for exact keyword matching
        - "semantic": use **Qdrant** only for semantic similarity search
        - "hybrid": use **both** tools - start with Elasticsearch, then Qdrant
    - Each tool can be called **only once**
    - When using Elasticsearch, use the **extracted keywords** as Action Input
    - When using Qdrant, use the **original question** as Action Input
    - You MUST base your answer on the retrieved data from tools
    - If no relevant data is found, clearly state that information is not available in the knowledge base
    - The final answer **must be in Vietnamese** and include specific details from the retrieved documents
    - Use **only** the 3 formats below. Do not invent new formats.

    ---
    Format 1 (using tool):
    Thought: [explain why you need to use the tool]
    Action: [Elasticsearch or Qdrant]
    Action Input: [keyword or original question]

    Format 2 (after having enough data):
    Thought: [explain why you can answer]
    Final Answer: [concise, focused answer in Vietnamese]

    ---

    Begin!

    Question: {question}
    Classification: {classification}
    """

    # ✅ invoke đúng định dạng agent yêu cầu
    response = summarize_answer_agent.invoke({"input": prompt})
    
    # response = safe_run_agent(node_response_agent, question)
    print(f"response: {response}")

    # Nếu agent trả về string, không có `.get('output')`
    if isinstance(response, str):
        state["answer"] = response
    elif isinstance(response, dict) and "output" in response:
        state["answer"] = response["output"]
    else:
        state["answer"] = "[Error] Unable to get result from agent"

    return state
