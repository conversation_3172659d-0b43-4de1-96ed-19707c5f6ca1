"""
Simple sync Qdrant utilities without async dependencies
"""
from qdrant_client import QdrantClient
from langchain_community.vectorstores import Qdrant as QdrantVectorStore
from app.config.index import settings
import logging
from typing import List, Any
import google.generativeai as genai

logger = logging.getLogger(__name__)

# Global instances for reuse
_qdrant_client = None
_embedding_model = None
_vectorstore = None


def get_qdrant_client_simple() -> QdrantClient:
    """Get simple Qdrant client"""
    global _qdrant_client
    if _qdrant_client is None:
        try:
            _qdrant_client = QdrantClient(
                host=settings.HOST_QDRANT, 
                port=6333,
                timeout=30
            )
            logger.info("Simple Qdrant client created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant client: {e}")
            raise
    return _qdrant_client


def get_embedding_model_simple():
    """Get simple embedding model using Gemini"""
    # Configure Gemini
    genai.configure(api_key=settings.API_KEY)

    def embed_query(text: str):
        """Embed a single query using Gemini"""
        try:
            response = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_query"
            )
            return response["embedding"]
        except Exception as e:
            logger.error(f"Gemini embedding failed: {e}")
            raise

    # Return a simple object with embed_query method
    return type('GeminiEmbedding', (), {'embed_query': embed_query})()


def get_qdrant_vectorstore_simple() -> QdrantVectorStore:
    """Get simple Qdrant vectorstore with Gemini embedding"""
    global _vectorstore
    if _vectorstore is None:
        try:
            client = get_qdrant_client_simple()
            embedding_model = get_embedding_model_simple()

            _vectorstore = QdrantVectorStore(
                client=client,
                collection_name=settings.QDRANT_COLLECTION,
                embeddings=embedding_model,
                content_payload_key="content",
                metadata_payload_key="metadata"
            )
            logger.info("Simple Qdrant vectorstore with Gemini created successfully")
        except Exception as e:
            logger.error(f"Failed to create simple Qdrant vectorstore with Gemini: {e}")
            raise
    return _vectorstore


def search_qdrant_simple(question: str) -> List[Any]:
    """
    Simple sync Qdrant search using Gemini embedding
    """
    try:
        # Configure Gemini
        genai.configure(api_key=settings.API_KEY)

        # Use direct Gemini embedding approach instead of LangChain vectorstore
        logger.info("Using Gemini embedding for Qdrant search...")
        return search_qdrant_fallback(question)

    except Exception as e:
        logger.error(f"Simple Qdrant search with Gemini failed: {e}")
        # Try with LangChain vectorstore as fallback
        try:
            logger.info("Attempting LangChain vectorstore fallback...")
            return search_qdrant_langchain_fallback(question)
        except Exception as fallback_error:
            logger.error(f"LangChain fallback also failed: {fallback_error}")
            return []


def search_qdrant_langchain_fallback(question: str) -> List[Any]:
    """
    Fallback using LangChain vectorstore with nomic-embed-text
    """
    try:
        # Get vectorstore
        vectorstore = get_qdrant_vectorstore_simple()

        # Create retriever with more conservative settings
        retriever = vectorstore.as_retriever(
            search_type="similarity",  # Use similarity instead of MMR to avoid potential issues
            search_kwargs={
                "k": 2,
                "fetch_k": 10  # Reduce fetch_k to avoid memory issues
            }
        )

        # Perform search
        logger.info(f"Performing LangChain Qdrant search for: {question[:50]}...")
        docs = retriever.invoke(question)

        logger.info(f"LangChain Qdrant search completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"LangChain Qdrant search failed: {e}")
        return []


def search_qdrant_fallback(question: str) -> List[Any]:
    """
    Fallback Qdrant search with minimal settings using Gemini embedding
    """
    try:
        # Configure Gemini
        genai.configure(api_key=settings.API_KEY)

        # Get client directly
        client = get_qdrant_client_simple()

        # Get embedding for the question using Gemini
        response = genai.embed_content(
            model="models/embedding-001",
            content=question,
            task_type="retrieval_query"
        )
        question_vector = response["embedding"]

        # Direct search using client
        search_result = client.search(
            collection_name=settings.QDRANT_COLLECTION,
            query_vector=question_vector,
            limit=2,
            with_payload=True
        )

        # Convert to LangChain document format
        docs = []
        for point in search_result:
            if point.payload:
                content = point.payload.get('content', '')
                metadata = point.payload.get('metadata', {})
                # Create a simple document-like object
                doc = type('Document', (), {
                    'page_content': content,
                    'metadata': metadata
                })()
                docs.append(doc)

        logger.info(f"Fallback Qdrant search with Gemini completed, found {len(docs)} documents")
        return docs

    except Exception as e:
        logger.error(f"Fallback Qdrant search with Gemini failed: {e}")
        return []


def health_check_qdrant() -> bool:
    """
    Check Qdrant health and collection status
    """
    try:
        client = get_qdrant_client_simple()

        # Check if collection exists
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]

        if settings.QDRANT_COLLECTION not in collection_names:
            logger.error(f"Collection '{settings.QDRANT_COLLECTION}' not found in Qdrant")
            return False

        # Get collection info
        collection_info = client.get_collection(settings.QDRANT_COLLECTION)
        logger.info(f"Qdrant collection '{settings.QDRANT_COLLECTION}' status: {collection_info.status}")
        logger.info(f"Points count: {collection_info.points_count}")
        logger.info(f"Vector size: {collection_info.config.params.vectors.size}")

        # Check if vector size matches Gemini embedding (768) or nomic (384/768)
        vector_size = collection_info.config.params.vectors.size
        if vector_size == 768:
            logger.info("Collection appears to use 768-dimensional embeddings (compatible with Gemini)")
        else:
            logger.info(f"Collection uses {vector_size}-dimensional embeddings")

        return collection_info.status == "green"

    except Exception as e:
        logger.error(f"Qdrant health check failed: {e}")
        return False


def close_qdrant_connections():
    """Close Qdrant connections"""
    global _qdrant_client, _embedding_model, _vectorstore

    try:
        if _qdrant_client:
            _qdrant_client.close()
            _qdrant_client = None

        _embedding_model = None
        _vectorstore = None

        logger.info("Qdrant connections closed")
    except Exception as e:
        logger.error(f"Error closing Qdrant connections: {e}")
