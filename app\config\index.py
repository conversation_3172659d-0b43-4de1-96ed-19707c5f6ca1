from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    # Core application settings
    MONGO_URI: str
    DATABASE: str
    API_KEY: str
    ORIGIN_PORTAL: str
    OR<PERSON>IN_PORTAL_DOCKER: str
    OR<PERSON>IN_PORTAL_IP: str
    ORIGIN_KONG: str
    ORIGIN_KONG_IP: str
    OLLAMA_URL: str
    HOST_QDRANT: str
    ES_URL: str
    QDRANT_COLLECTION: str
    ES_INDEX: str

    # Performance settings with defaults
    ENVIRONMENT: str = "development"
    WORKERS: int = 1
    MAX_CONNECTIONS: int = 20
    KEEP_ALIVE: int = 2

    # Cache settings
    CACHE_TTL_SEARCH: int = 300
    CACHE_TTL_AI: int = 300
    CACHE_TTL_EMBEDDING: int = 3600
    CACHE_TTL_CLASSIFICATION: int = 1800

    # Database settings
    DB_POOL_SIZE: int = 10
    DB_MIN_POOL_SIZE: int = 2
    DB_MAX_IDLE_TIME: int = 30000
    DB_SERVER_SELECTION_TIMEOUT: int = 5000
    DB_CONNECT_TIMEOUT: int = 10000
    DB_SOCKET_TIMEOUT: int = 20000

    # Vector store settings
    QDRANT_TIMEOUT: int = 30
    ES_MAX_RETRIES: int = 2
    ES_TIMEOUT: int = 15
    ES_MAX_TIMEOUT: int = 30

    # LLM settings
    LLM_TEMPERATURE: float = 0.3
    LLM_NUM_PREDICT: int = 256
    LLM_TOP_K: int = 10
    LLM_TOP_P: float = 0.9

    # Search settings
    SEARCH_K: int = 2
    SEARCH_FETCH_K: int = 10
    SEARCH_LAMBDA_MULT: float = 0.7

    # Monitoring settings
    SLOW_REQUEST_THRESHOLD: float = 2.0
    LOG_LEVEL: str = "DEBUG"

    class Config:
        # Auto-detect environment file
        env_file = ".env"  # Default to .env for development

        @classmethod
        def customise_sources(cls, init_settings, env_settings, file_secret_settings):
            # Check if we're in production
            environment = os.getenv("ENVIRONMENT", "development")
            if environment == "production":
                cls.env_file = ".env.production"
            else:
                cls.env_file = ".env"  # Use .env for development
            return init_settings, env_settings, file_secret_settings


settings = Settings()
