import requests
import json

# Thông tin Qdrant
QDRANT_HOST = "http://*************"
QDRANT_PORT = 6333
COLLECTION_NAME = "collection_ssg_detail_ver3"

# Cấ<PERSON> hình collection
payload = {
    "vectors": {
        "size": 768,
        "distance": "Cosine"
    },
}

def create_collection():
    url = f"{QDRANT_HOST}:{QDRANT_PORT}/collections/{COLLECTION_NAME}"
    headers = {"Content-Type": "application/json"}
    response = requests.put(url, headers=headers, data=json.dumps(payload))

    if response.status_code == 200:
        print("✅ Collection created successfully.")
        print(response.json())
    else:
        print(f"❌ Failed to create collection. Status code: {response.status_code}")
        print("Response:", response.text)

if __name__ == "__main__":
    create_collection()
