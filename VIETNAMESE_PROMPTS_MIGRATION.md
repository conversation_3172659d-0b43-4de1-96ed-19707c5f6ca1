# 🇻🇳 Vietnamese Prompts Migration Guide

## Tổng quan
Tài liệu này mô tả việc chuyển đổi toàn bộ prompts từ tiếng <PERSON>h sang tiếng Việt để cải thiện chất lượng và tính tự nhiên của câu trả lời.

## 🎯 Lý do chuyển đổi

### **Tại sao prompt tiếng Việt tốt hơn:**
1. **Hiểu ngữ cảnh tự nhiên**: Model hiểu trực tiếp ý định mà không cần "dịch" từ English thinking
2. **Tone phù hợp**: Giọng điệu chuyên nghiệp nhưng thân thiện theo văn hóa Việt
3. **Cấu trúc câu tự nhiên**: Sử dụng từ nối và cách diễn đạt phù hợp
4. **Domain-specific terms**: Hiể<PERSON> rõ thuật ngữ và tên riêng Việt Nam

## 📝 Các prompts đã chuyển đổi

### 1. **Classification Prompt**
**File:** `app/engine/chains/classification_llm_chain.py`

#### Trước:
```
You are a question classification module.
Classify the following question into one of three categories...
```

#### Sau:
```
Bạn là module phân loại câu hỏi.
Hãy phân loại câu hỏi sau đây vào một trong ba danh mục...
```

**Cải tiến:**
- ✅ Hướng dẫn rõ ràng bằng tiếng Việt
- ✅ Ví dụ sử dụng câu hỏi tiếng Việt thực tế
- ✅ Định nghĩa phù hợp với ngữ cảnh Việt Nam

### 2. **Keywords Extraction Prompt**
**File:** `app/engine/chains/keywords_llm_chain.py`

#### Trước:
```
You are a keyword extraction module for keyword-based search.
Your task is to extract clear, specific keywords...
```

#### Sau:
```
Bạn là module trích xuất từ khóa cho tìm kiếm dựa trên từ khóa.
Nhiệm vụ của bạn là trích xuất các từ khóa rõ ràng, cụ thể...
```

**Cải tiến:**
- ✅ Hướng dẫn chi tiết bằng tiếng Việt
- ✅ Ví dụ với từ khóa tiếng Việt: "AI Portal, Chính sách lương"
- ✅ Ưu tiên danh từ riêng và thuật ngữ Việt

### 3. **Answer Synthesis Prompt**
**File:** `app/engine/chains/answer_synthesis_chain.py`

#### Trước:
```
You are an expert analyst and information synthesizer...
Answer in Vietnamese, structured clearly...
```

#### Sau:
```
Bạn là chuyên gia phân tích và tổng hợp thông tin của Tập đoàn Sao Mai Solution Group.
NHIỆM VỤ: Dựa trên dữ liệu được truy xuất từ hệ thống tri thức nội bộ...
```

**Cải tiến:**
- ✅ Prompt hoàn toàn bằng tiếng Việt
- ✅ Nguyên tắc và hướng dẫn rõ ràng
- ✅ Cấu trúc câu trả lời chuẩn Việt Nam

### 4. **Tool Descriptions**
**File:** `app/engine/react/tools/index.py`

#### Trước:
```
"Search for documents by keyword using Elasticsearch."
"Semantic search for documents using Qdrant vector similarity."
```

#### Sau:
```
"Tìm kiếm tài liệu theo từ khóa sử dụng Elasticsearch..."
"Tìm kiếm ngữ nghĩa cho tài liệu sử dụng độ tương đồng vector Qdrant..."
```

**Cải tiến:**
- ✅ Mô tả công cụ bằng tiếng Việt
- ✅ Hướng dẫn sử dụng rõ ràng
- ✅ Phù hợp với agent reasoning

### 5. **Response Node Prompt**
**File:** `app/engine/graph_nodes/nodes/node_response.py`

#### Trước:
```
You are an AI assistant specializing in Sao Mai Solution Group's internal knowledge base.
IMPORTANT RULES: You MUST use the search tools...
```

#### Sau:
```
Bạn là trợ lý AI chuyên về cơ sở tri thức nội bộ của Tập đoàn Sao Mai Solution Group.
QUY TẮC QUAN TRỌNG: Bạn PHẢI sử dụng các công cụ tìm kiếm...
```

**Cải tiến:**
- ✅ Quy tắc và định dạng bằng tiếng Việt
- ✅ Hướng dẫn sử dụng công cụ rõ ràng
- ✅ Format phù hợp với agent workflow

### 6. **General LLM Chain**
**File:** `app/engine/chains/llm_chain.py`

#### Đã có sẵn tiếng Việt, được cải thiện:
```
Bạn là trợ lý AI thông minh của Tập đoàn Sao Mai Solution Group.
Hãy luôn trả lời bằng tiếng Việt và cố gắng hỗ trợ người dùng tốt nhất có thể.
```

## 📊 Kết quả test

### **Classification Test:**
```
Câu hỏi: 'Sao Mai Solution Group cung cấp dịch vụ gì?'
Phân loại: hybrid ✅

Câu hỏi: 'Lệnh git để tạo branch mới là gì?'
Phân loại: keyword ✅
```

### **Keywords Extraction Test:**
```
Câu hỏi: 'Sao Mai Solution Group có những sản phẩm AI nào?'
Từ khóa: AI, Sao Mai Solution Group ✅

Câu hỏi: 'Chính sách lương thưởng của công ty ra sao?'
Từ khóa: Chính sách lương, Công ty ✅
```

### **Answer Synthesis Test:**
```
Câu trả lời được tổng hợp:
"Sao Mai Solution Group cung cấp các dịch vụ phần mềm doanh nghiệp, 
phát triển ứng dụng di động, và dịch vụ tư vấn công nghệ thông tin..."
```
✅ Câu trả lời tự nhiên, có cấu trúc, phù hợp văn hóa Việt

## 🚀 Lợi ích đạt được

### **Chất lượng câu trả lời:**
- **Tự nhiên hơn**: Không còn cảm giác "translated"
- **Tone phù hợp**: Chuyên nghiệp nhưng thân thiện
- **Cấu trúc tốt**: Sử dụng bullet points, đánh số phù hợp

### **Hiểu ngữ cảnh:**
- **Domain terms**: Hiểu rõ "Tập đoàn", "dịch vụ", "giải pháp"
- **Cultural context**: Cách xưng hô và diễn đạt phù hợp
- **Technical terms**: Kết hợp thuật ngữ Anh-Việt tự nhiên

### **Performance:**
- **Better classification**: Phân loại chính xác hơn với câu hỏi tiếng Việt
- **Relevant keywords**: Trích xuất từ khóa phù hợp hơn
- **Coherent answers**: Câu trả lời mạch lạc, logic

## 🔧 Cách sử dụng

### **Test prompts:**
```bash
python test_vietnamese_prompts.py
```

### **Sử dụng trong production:**
Tất cả prompts đã được cập nhật tự động. Không cần thay đổi code gọi.

## 📈 So sánh Before/After

### **Before (English prompts):**
```
Input: "Sao Mai Solution Group cung cấp dịch vụ gì?"
Output: "Based on the provided information, Sao Mai Solution Group provides..."
→ Cảm giác "translated", không tự nhiên
```

### **After (Vietnamese prompts):**
```
Input: "Sao Mai Solution Group cung cấp dịch vụ gì?"
Output: "Sao Mai Solution Group cung cấp các dịch vụ phần mềm doanh nghiệp..."
→ Tự nhiên, mạch lạc, phù hợp văn hóa
```

## 🎯 Kết luận

Việc chuyển đổi prompts sang tiếng Việt đã mang lại:
- **Câu trả lời chất lượng cao hơn** với tone tự nhiên
- **Hiểu ngữ cảnh tốt hơn** cho domain Việt Nam
- **User experience tốt hơn** với responses phù hợp văn hóa
- **Consistency** trong toàn bộ hệ thống

Hệ thống hiện tại đã sẵn sàng cung cấp trải nghiệm hoàn toàn bằng tiếng Việt! 🇻🇳
