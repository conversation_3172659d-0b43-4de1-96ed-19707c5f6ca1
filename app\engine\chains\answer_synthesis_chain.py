"""
Chain để tổng hợp câu trả lời từ dữ liệu ES và Qdrant
"""
from langchain_ollama import OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

answer_synthesis_template = """
You are an expert analyst and information synthesizer for Sao Mai Solution Group.

TASK: Based on the data retrieved from the internal knowledge system, answer the following question clearly, accurately, and in detail.

PRINCIPLES:
1. Use only the information provided in the data sources below.
2. Answer in Vietnamese, structured clearly and professionally.

DATA FROM ELASTICSEARCH:
{es_data}

DATA FROM QDRANT:
{qdrant_data}

QUESTION: {question}

ANSWER GUIDELINES:
- If relevant data exists: synthesize information from both sources into a cohesive professional report, without naming technical sources like "Elasticsearch" or "Qdrant".
- If no relevant data is found: state clearly that there is no available information on the topic.

ANSWER:
"""
# - Answer structure: Brief introduction → Main details → Additional context (if applicable)
# You are an expert analyst and information synthesizer for Sao Mai Solution Group.

# TASK: Based on the data provided from the search system, answer the question accurately and in detail.

# PRINCIPLES:
# 1. ONLY use information from the provided data
# 2. If no relevant information is found, clearly state "No information found about..."
# 3. Quote specifically from documents when possible
# 4. Answer in Vietnamese, clearly and with structure

# DATA FROM ELASTICSEARCH:
# {es_data}

# DATA FROM QDRANT:
# {qdrant_data}

# QUESTION: {question}

# ANSWER GUIDELINES:
# - If data exists: Synthesize information from both ES and Qdrant, prioritize specific and detailed information
# - If no data: Clearly state "Currently no information about [topic] is available in Sao Mai Solution Group's database"
# - Answer structure: Brief introduction -> Main details -> Additional information (if any)

# ANSWER:

answer_synthesis_prompt = ChatPromptTemplate.from_template(answer_synthesis_template)
parser = StrOutputParser()

answer_synthesis_chain = answer_synthesis_prompt | llm | parser


def synthesize_answer(question: str, es_data: list, qdrant_data: list) -> str:
    """
    Tổng hợp câu trả lời từ dữ liệu ES và Qdrant
    """
    # Format ES data
    es_formatted = ""
    if es_data:
        es_formatted = "Data from Elasticsearch:\n"
        for i, item in enumerate(es_data[:3]):  # Limit to 3 items
            if isinstance(item, dict) and '_source' in item:
                source = item['_source']
                es_formatted += f"- Document {i+1}:\n"

                # Extract key information
                if 'keyword' in source:
                    es_formatted += f"  Keywords: {source['keyword']}\n"
                if 'text' in source:
                    text = source['text'][:300] + "..." if len(source['text']) > 300 else source['text']
                    es_formatted += f"  Content: {text}\n"
                if 'metadata' in source:
                    es_formatted += f"  Source: {source['metadata']}\n"
                es_formatted += "\n"
    else:
        es_formatted = "No data from Elasticsearch.\n"
    
    # Format Qdrant data
    qdrant_formatted = ""
    if qdrant_data:
        qdrant_formatted = "Data from Qdrant (semantic search):\n"
        for i, doc in enumerate(qdrant_data[:3]):  # Limit to 3 items
            qdrant_formatted += f"- Document {i+1}:\n"

            if hasattr(doc, 'page_content'):
                content = doc.page_content[:300] + "..." if len(doc.page_content) > 300 else doc.page_content
                qdrant_formatted += f"  Content: {content}\n"

            if hasattr(doc, 'metadata') and doc.metadata:
                if 'source' in doc.metadata:
                    qdrant_formatted += f"  Source: {doc.metadata['source']}\n"
            qdrant_formatted += "\n"
    else:
        qdrant_formatted = "No data from Qdrant.\n"
    
    # Generate answer
    try:
        answer = answer_synthesis_chain.invoke({
            "question": question,
            "es_data": es_formatted,
            "qdrant_data": qdrant_formatted
        })
        return answer.strip()
    except Exception as e:
        return f"Error synthesizing answer: {e}"


def format_no_data_response(question: str) -> str:
    """
    Format response when no data is available
    """
    return f"""Currently no specific information about "{question}" is available in Sao Mai Solution Group's database.

For better support, you can:
- Try using different keywords
- Contact the technical department directly
- Check detailed documentation

Sorry for not being able to provide accurate information for this question."""
