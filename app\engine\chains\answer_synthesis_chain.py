"""
Chain để tổng hợp câu trả lời từ dữ liệu ES và Qdrant
"""
from langchain_ollama import OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings
import re
from typing import List, Dict, Any

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

answer_synthesis_template = """
Bạn là chuyên gia phân tích và tổng hợp thông tin của Tập đoàn Sao Mai Solution Group.

NHIỆM VỤ: Dựa trên dữ liệu được truy xuất từ hệ thống tri thức nội bộ, hãy trả lời câu hỏi một cách rõ ràng, chính xác và chi tiết.

NGUYÊN TẮC QUAN TRỌNG:
1. CHỈ sử dụng thông tin từ các nguồn dữ liệu được cung cấp bên dưới
2. Ưu tiên thông tin có độ chính xác và liên quan cao nhất
3. Tổng hợp thông tin từ nhiều nguồn để tạo câu trả lời toàn diện
4. Trả lời bằng tiếng Việt, có cấu trúc rõ ràng và chuyên nghiệp

DỮ LIỆU TỪ TÌM KIẾM TỪ KHÓA:
{es_data}

DỮ LIỆU TỪ TÌM KIẾM NGỮ NGHĨA:
{qdrant_data}

CÂU HỎI: {question}

HƯỚNG DẪN TRẢ LỜI:
- Nếu có dữ liệu liên quan: Tổng hợp thông tin từ cả hai nguồn thành một báo cáo chuyên nghiệp, mạch lạc. Không đề cập đến tên các nguồn kỹ thuật.
- Nếu không tìm thấy dữ liệu liên quan: Nói rõ rằng hiện không có thông tin về chủ đề này.
- Cấu trúc câu trả lời: Giới thiệu ngắn gọn → Chi tiết chính → Thông tin bổ sung (nếu có)
- Sử dụng bullet points hoặc đánh số khi cần thiết để tăng tính rõ ràng
- Đảm bảo câu trả lời trực tiếp giải quyết câu hỏi được đặt ra

CÂU TRẢ LỜI:
"""
# - Answer structure: Brief introduction → Main details → Additional context (if applicable)
# You are an expert analyst and information synthesizer for Sao Mai Solution Group.

# TASK: Based on the data provided from the search system, answer the question accurately and in detail.

# PRINCIPLES:
# 1. ONLY use information from the provided data
# 2. If no relevant information is found, clearly state "No information found about..."
# 3. Quote specifically from documents when possible
# 4. Answer in Vietnamese, clearly and with structure

# DATA FROM ELASTICSEARCH:
# {es_data}

# DATA FROM QDRANT:
# {qdrant_data}

# QUESTION: {question}

# ANSWER GUIDELINES:
# - If data exists: Synthesize information from both ES and Qdrant, prioritize specific and detailed information
# - If no data: Clearly state "Currently no information about [topic] is available in Sao Mai Solution Group's database"
# - Answer structure: Brief introduction -> Main details -> Additional information (if any)

# ANSWER:

answer_synthesis_prompt = ChatPromptTemplate.from_template(answer_synthesis_template)
parser = StrOutputParser()

answer_synthesis_chain = answer_synthesis_prompt | llm | parser


def calculate_content_similarity(text1: str, text2: str) -> float:
    """
    Calculate similarity between two texts using simple word overlap
    """
    if not text1 or not text2:
        return 0.0

    # Simple word-based similarity
    words1 = set(re.findall(r'\w+', text1.lower()))
    words2 = set(re.findall(r'\w+', text2.lower()))

    if not words1 or not words2:
        return 0.0

    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))

    return intersection / union if union > 0 else 0.0


def deduplicate_and_rank_content(es_data: List[Dict], qdrant_data: List[Any]) -> tuple:
    """
    Remove duplicate content and rank by relevance
    """
    # Extract content from both sources
    all_contents = []

    # Process ES data
    for item in es_data:
        if isinstance(item, dict) and '_source' in item:
            source = item['_source']
            content = source.get('text', '')
            if content and len(content.strip()) > 20:  # Filter very short content
                all_contents.append({
                    'content': content,
                    'source': 'es',
                    'score': item.get('_score', 0),
                    'metadata': source.get('metadata', ''),
                    'keywords': source.get('keyword', ''),
                    'original': item
                })

    # Process Qdrant data
    for doc in qdrant_data:
        if hasattr(doc, 'page_content') and doc.page_content:
            content = doc.page_content.strip()
            if len(content) > 20:  # Filter very short content
                all_contents.append({
                    'content': content,
                    'source': 'qdrant',
                    'score': 1.0,  # Default score for qdrant
                    'metadata': getattr(doc, 'metadata', {}),
                    'keywords': '',
                    'original': doc
                })

    # Remove duplicates based on content similarity
    unique_contents = []
    for content_item in all_contents:
        is_duplicate = False
        for existing in unique_contents:
            similarity = calculate_content_similarity(
                content_item['content'],
                existing['content']
            )
            if similarity > 0.8:  # 80% similarity threshold
                is_duplicate = True
                # Keep the one with higher score
                if content_item['score'] > existing['score']:
                    unique_contents.remove(existing)
                    unique_contents.append(content_item)
                break

        if not is_duplicate:
            unique_contents.append(content_item)

    # Sort by score (descending)
    unique_contents.sort(key=lambda x: x['score'], reverse=True)

    # Separate back to ES and Qdrant format
    filtered_es = [item['original'] for item in unique_contents if item['source'] == 'es']
    filtered_qdrant = [item['original'] for item in unique_contents if item['source'] == 'qdrant']

    return filtered_es[:4], filtered_qdrant[:4]  # Limit to top 4 each


def synthesize_answer(question: str, es_data: list, qdrant_data: list) -> str:
    """
    Tổng hợp câu trả lời từ dữ liệu ES và Qdrant với cải thiện chất lượng
    """
    # Apply deduplication and ranking
    es_data, qdrant_data = deduplicate_and_rank_content(es_data, qdrant_data)
    # Format ES data with better structure and relevance scoring
    es_formatted = ""
    if es_data:
        es_formatted = "Thông tin từ tìm kiếm từ khóa:\n"
        # Sort by score if available
        sorted_es_data = sorted(es_data, key=lambda x: x.get('_score', 0), reverse=True)

        for i, item in enumerate(sorted_es_data[:4]):  # Increase to 4 items for better coverage
            if isinstance(item, dict) and '_source' in item:
                source = item['_source']
                score = item.get('_score', 0)
                es_formatted += f"[Tài liệu {i+1} - Độ liên quan: {score:.2f}]\n"

                # Extract and format key information
                if 'keyword' in source and source['keyword']:
                    es_formatted += f"Từ khóa: {source['keyword']}\n"

                if 'text' in source and source['text']:
                    # Intelligent text truncation - try to keep complete sentences
                    text = source['text']
                    if len(text) > 400:
                        # Find last complete sentence within 400 chars
                        truncated = text[:400]
                        last_period = truncated.rfind('.')
                        if last_period > 200:  # If we found a reasonable sentence break
                            text = truncated[:last_period + 1] + "..."
                        else:
                            text = truncated + "..."
                    es_formatted += f"Nội dung: {text}\n"

                if 'metadata' in source and source['metadata']:
                    es_formatted += f"Nguồn: {source['metadata']}\n"
                es_formatted += "\n"
    else:
        es_formatted = "Không có dữ liệu từ tìm kiếm từ khóa.\n"
    
    # Format Qdrant data with better structure
    qdrant_formatted = ""
    if qdrant_data:
        qdrant_formatted = "Thông tin từ tìm kiếm ngữ nghĩa:\n"

        # Filter and sort by relevance if possible
        relevant_docs = []
        for doc in qdrant_data[:4]:  # Increase to 4 items
            if hasattr(doc, 'page_content') and doc.page_content.strip():
                relevant_docs.append(doc)

        for i, doc in enumerate(relevant_docs):
            qdrant_formatted += f"[Tài liệu {i+1} - Tìm kiếm ngữ nghĩa]\n"

            if hasattr(doc, 'page_content') and doc.page_content:
                content = doc.page_content.strip()
                # Intelligent content truncation
                if len(content) > 400:
                    # Try to keep complete sentences
                    truncated = content[:400]
                    last_period = truncated.rfind('.')
                    if last_period > 200:
                        content = truncated[:last_period + 1] + "..."
                    else:
                        content = truncated + "..."
                qdrant_formatted += f"Nội dung: {content}\n"

            # Extract metadata information
            if hasattr(doc, 'metadata') and doc.metadata:
                if isinstance(doc.metadata, dict):
                    if 'source' in doc.metadata:
                        qdrant_formatted += f"Nguồn: {doc.metadata['source']}\n"
                    # Add other relevant metadata fields
                    for key, value in doc.metadata.items():
                        if key not in ['source'] and value and len(str(value)) < 100:
                            qdrant_formatted += f"{key.title()}: {value}\n"
                else:
                    qdrant_formatted += f"Nguồn: {doc.metadata}\n"
            qdrant_formatted += "\n"
    else:
        qdrant_formatted = "Không có dữ liệu từ tìm kiếm ngữ nghĩa.\n"
    
    # Generate answer
    try:
        answer = answer_synthesis_chain.invoke({
            "question": question,
            "es_data": es_formatted,
            "qdrant_data": qdrant_formatted
        })
        return answer.strip()
    except Exception as e:
        return f"Error synthesizing answer: {e}"


def format_no_data_response(question: str) -> str:
    """
    Format response when no data is available
    """
    return f"""Hiện tại không có thông tin cụ thể về "{question}" trong cơ sở dữ liệu của Tập đoàn Sao Mai Solution Group.

Để được hỗ trợ tốt hơn, bạn có thể:
• Thử sử dụng các từ khóa khác nhau
• Liên hệ trực tiếp với bộ phận kỹ thuật
• Kiểm tra tài liệu chi tiết
• Đặt câu hỏi cụ thể hơn về chủ đề bạn quan tâm

Xin lỗi vì không thể cung cấp thông tin chính xác cho câu hỏi này."""


def assess_answer_quality(question: str, es_data: list, qdrant_data: list) -> Dict[str, Any]:
    """
    Assess the quality of available data for answering the question
    """
    total_sources = len(es_data) + len(qdrant_data)

    # Calculate content length
    total_content_length = 0
    for item in es_data:
        if isinstance(item, dict) and '_source' in item:
            total_content_length += len(item['_source'].get('text', ''))

    for doc in qdrant_data:
        if hasattr(doc, 'page_content'):
            total_content_length += len(doc.page_content)

    # Simple quality assessment
    quality_score = min(1.0, (total_sources * 0.2) + (min(total_content_length, 2000) / 2000 * 0.8))

    return {
        'total_sources': total_sources,
        'total_content_length': total_content_length,
        'quality_score': quality_score,
        'has_sufficient_data': quality_score > 0.3
    }
