"""
Vector Store Manager - Singleton pattern để tối ưu vector store connections
"""
from qdrant_client import QdrantClient
from langchain_community.vectorstores import Qdrant as QdrantVectorStore
import requests
from app.config.index import settings
from app.core.llm_manager import get_embedding_model
import logging
from typing import Optional
import asyncio
import google.generativeai as genai

logger = logging.getLogger(__name__)


class VectorStoreManager:
    """Singleton class để quản lý vector store connections"""

    _instance: Optional['VectorStoreManager'] = None
    _qdrant_client: Optional[QdrantClient] = None
    _qdrant_vectorstore: Optional[QdrantVectorStore] = None
    _es_session: Optional[requests.Session] = None
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def get_qdrant_client(self) -> QdrantClient:
        """Lấy Qdrant client với connection pooling"""
        if self._qdrant_client is None:
            async with self._lock:
                if self._qdrant_client is None:
                    try:
                        self._qdrant_client = QdrantClient(
                            host=settings.HOST_QDRANT, 
                            port=6333,
                            timeout=30,  # Timeout 30s
                            # Connection pooling
                            prefer_grpc=True,
                        )
                        logger.info("Qdrant client created successfully")
                    except Exception as e:
                        logger.error(f"Failed to create Qdrant client: {e}")
                        raise
        return self._qdrant_client
    
    async def get_qdrant_vectorstore(self) -> QdrantVectorStore:
        """Lấy Qdrant vectorstore với caching"""
        if self._qdrant_vectorstore is None:
            async with self._lock:
                if self._qdrant_vectorstore is None:
                    try:
                        client = await self.get_qdrant_client()
                        embedding_model = await get_embedding_model()
                        
                        self._qdrant_vectorstore = QdrantVectorStore(
                            client=client,
                            collection_name=settings.QDRANT_COLLECTION,
                            embeddings=embedding_model,
                            content_payload_key="content",
                            metadata_payload_key="metadata"
                        )
                        logger.info("Qdrant vectorstore created successfully")
                    except Exception as e:
                        logger.error(f"Failed to create Qdrant vectorstore: {e}")
                        raise
        return self._qdrant_vectorstore
    
    async def get_es_session(self) -> requests.Session:
        """Lấy Elasticsearch session với connection pooling"""
        if self._es_session is None:
            async with self._lock:
                if self._es_session is None:
                    try:
                        self._es_session = requests.Session()
                        # Configure session for better performance
                        adapter = requests.adapters.HTTPAdapter(
                            pool_connections=10,
                            pool_maxsize=20,
                            max_retries=3,
                            pool_block=False
                        )
                        self._es_session.mount('http://', adapter)
                        self._es_session.mount('https://', adapter)

                        # Test connection
                        response = self._es_session.get(f"{settings.ES_URL}/_cluster/health", timeout=10)
                        if response.status_code == 200:
                            logger.info("Elasticsearch session created successfully")
                        else:
                            logger.warning(f"Elasticsearch health check returned: {response.status_code}")
                    except Exception as e:
                        logger.error(f"Failed to create Elasticsearch session: {e}")
                        raise
        return self._es_session
    
    async def close_connections(self):
        """Đóng tất cả connections khi shutdown"""
        if self._qdrant_client:
            self._qdrant_client.close()
        if self._es_session:
            self._es_session.close()


# Global instance
vector_manager = VectorStoreManager()


# Utility functions
async def get_qdrant_client() -> QdrantClient:
    """Helper function để lấy Qdrant client"""
    return await vector_manager.get_qdrant_client()


async def get_qdrant_vectorstore() -> QdrantVectorStore:
    """Helper function để lấy Qdrant vectorstore"""
    return await vector_manager.get_qdrant_vectorstore()


async def get_es_session() -> requests.Session:
    """Helper function để lấy Elasticsearch session"""
    return await vector_manager.get_es_session()


def configure_gemini():
    """Configure Gemini API (lightweight operation)"""
    genai.configure(api_key=settings.API_KEY)
    return genai
