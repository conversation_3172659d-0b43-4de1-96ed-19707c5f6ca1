#!/usr/bin/env python3
import sys
sys.path.append('.')
from app.engine.chains.classification_llm_chain import classification_llm_chain

# Test empty string case
result = classification_llm_chain.invoke({'question': ''})
print(f'Raw result: "{result}"')

# Test extraction logic
classification_clean = result.strip().lower()
valid_classifications = ['keyword', 'semantic', 'hybrid']
extracted_classification = None

for valid_class in valid_classifications:
    if valid_class in classification_clean:
        extracted_classification = valid_class
        break

if extracted_classification:
    classification_clean = extracted_classification
elif classification_clean not in valid_classifications:
    classification_clean = 'hybrid'

print(f'Final result: "{classification_clean}"')
