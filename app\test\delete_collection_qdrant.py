import requests

# Thông tin Qdrant server
QDRANT_HOST = "http://127.0.0.1"
QDRANT_PORT = 6333
COLLECTION_NAME = "collection_ssg_detail_ver3"

def delete_collection():
    url = f"{QDRANT_HOST}:{QDRANT_PORT}/collections/{COLLECTION_NAME}"
    response = requests.delete(url)

    if response.status_code == 200:
        print("✅ Collection deleted successfully.")
        print(response.json())
    else:
        print(f"❌ Failed to delete collection. Status code: {response.status_code}")
        print("Response:", response.text)

if __name__ == "__main__":
    delete_collection()
