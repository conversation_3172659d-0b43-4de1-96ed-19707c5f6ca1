import requests

ES_HOST = 'http://localhost:9200'
INDEX = 'index_ssg_detail_ver2'
BATCH_SIZE = 2000 # Số document mỗi lần lấy

def fetch_all_documents():
    all_docs = []
    from_ = 0

    while True:
        query = {
            "query": {
                "match_all": {}
            },
            "from": from_,
            "size": BATCH_SIZE
        }

        response = requests.get(f"{ES_HOST}/{INDEX}/_search", json=query)
        response.raise_for_status()
        data = response.json()

        hits = data['hits']['hits']
        if not hits:
            break

        for hit in hits:
            all_docs.append(hit['_source'])
            print(hit['_source'])  # In ra từng document

        from_ += BATCH_SIZE

    print(f"Đã lấy tổng cộng {len(all_docs)} document.")
    return all_docs

if __name__ == '__main__':
    fetch_all_documents()
