# import keywords_llm_chain từ file keywords_llm_chain.py (giả sử cùng thư mục hoặc import đúng)
from app.engine.chains.keywords_llm_chain import keywords_llm_chain

def node_keywords(state):
    classification = state.get("classification")
    if classification in ["keyword", "hybrid"]:
        question = state.get("question", "")
        # Gọi llm để trích xuất keywords
        keywords_str = keywords_llm_chain.invoke({"question": question})
        state["keywords"] = keywords_str 
        # print(f"[NODE_KEYWORD]Extracted keywords: {keywords_str}")
        state["keyword_node_processed"] = True
    else:
        state["keyword_node_processed"] = False
    return state