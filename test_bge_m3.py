#!/usr/bin/env python3
"""
Test script for BGE-M3 model integration
"""
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

from app.utils.qdrant_simple import get_embedding_model_simple, health_check_qdrant

def test_bge_m3_embedding():
    """Test BGE-M3 embedding functionality"""
    print("=" * 50)
    print("Testing BGE-M3 Embedding Model")
    print("=" * 50)
    
    try:
        # Load the model
        print("Loading BGE-M3 model...")
        model = get_embedding_model_simple()
        print("✓ Model loaded successfully!")
        
        # Test embedding
        test_texts = [
            "Hello world",
            "What is artificial intelligence?",
            "BGE-M3 is a multilingual embedding model",
            "Xin chào thế giới"  # Vietnamese text
        ]
        
        print("\nTesting embeddings:")
        for i, text in enumerate(test_texts, 1):
            print(f"\n{i}. Text: '{text}'")
            embedding = model.embed_query(text)
            print(f"   Embedding dimension: {len(embedding)}")
            print(f"   First 5 values: {embedding[:5]}")
            
        print("\n✓ All embedding tests passed!")
        
    except Exception as e:
        print(f"✗ Error during embedding test: {e}")
        return False
    
    return True

def test_qdrant_health():
    """Test Qdrant health check"""
    print("\n" + "=" * 50)
    print("Testing Qdrant Health Check")
    print("=" * 50)
    
    try:
        result = health_check_qdrant()
        if result:
            print("✓ Qdrant health check passed!")
        else:
            print("⚠ Qdrant health check failed (but this is expected if collection dimensions don't match)")
        return True
    except Exception as e:
        print(f"✗ Qdrant health check error: {e}")
        return False

def main():
    """Main test function"""
    print("BGE-M3 Integration Test")
    print("=" * 50)
    
    # Test embedding
    embedding_success = test_bge_m3_embedding()
    
    # Test Qdrant health (optional, may fail due to connection issues)
    health_success = test_qdrant_health()
    
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    print(f"BGE-M3 Embedding: {'✓ PASS' if embedding_success else '✗ FAIL'}")
    print(f"Qdrant Health: {'✓ PASS' if health_success else '⚠ WARNING'}")
    
    if embedding_success:
        print("\n🎉 BGE-M3 model is working correctly!")
        print("Note: If Qdrant collection dimensions don't match (4096 vs 1024),")
        print("you'll need to recreate the collection with 1024 dimensions.")
    else:
        print("\n❌ BGE-M3 model test failed!")
    
    return embedding_success

if __name__ == "__main__":
    main()
