from motor.motor_asyncio import AsyncIOMotorClient
from ..config.index import settings
import logging

logger = logging.getLogger(__name__)

# Tối ưu connection với connection pooling
client = AsyncIOMotorClient(
    settings.MONGO_URI,
    maxPoolSize=50,  # Tối đa 50 connections
    minPoolSize=10,  # Tối thiểu 10 connections
    maxIdleTimeMS=30000,  # Timeout cho idle connections
    serverSelectionTimeoutMS=5000,  # Timeout khi chọn server
    connectTimeoutMS=10000,  # Timeout khi connect
    socketTimeoutMS=20000,  # Timeout cho socket operations
)

db = client[settings.DATABASE]


async def get_db():
    """
    Dependency để lấy database instance
    Sử dụng connection pooling để tối ưu performance
    """
    try:
        # Ping để kiểm tra connection
        await client.admin.command('ping')
        return db
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise


async def close_db_connection():
    """Đóng database connection khi shutdown"""
    client.close()
