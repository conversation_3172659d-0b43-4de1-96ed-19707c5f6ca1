import logging

logger = logging.getLogger(__name__)

def edge_after_classify(state):
    classification = state.get('classification')
    logger.info(f"Edge after classify: classification = {classification}")

    if classification == "keyword":
        return "keyword"
    elif classification == "hybrid":
        return "hybrid"
    elif classification == "semantic":
        return "retrieve"
    else:
        # Default to hybrid if classification is None or unexpected value
        logger.warning(f"Unexpected classification '{classification}', defaulting to 'hybrid'")
        return "hybrid"
    
def edge_after_search(state):
    es_data = state.get("es_data")
    if es_data and len(es_data) > 0:
        return "response"
    else:
        return "retrieve"


def edge_after_retrieve(state):
    qdrant_data = state.get("qdrant_data")
    if qdrant_data and len(qdrant_data) > 0:
        return "response"
    else:
        return "skip_search_or_retrieve"

def edge_after_hybrid(state):
    # Always go to response after hybrid search
    # The response node will handle empty data cases
    logger.info(f"Edge after hybrid: es_data={bool(state.get('es_data'))}, qdrant_data={bool(state.get('qdrant_data'))}")
    return "response"