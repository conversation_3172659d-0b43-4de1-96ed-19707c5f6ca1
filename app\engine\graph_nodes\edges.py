def edge_after_classify(state):
    classification = state.get('classification')
    if classification == "keyword":
        return "keyword"
    elif classification == "hybrid":
        return "hybrid"
    elif classification == "semantic": 
        return "retrieve"  
    
def edge_after_search(state):
    if state.get("es_data"):
        return "response"   
    else: 
        return "retrieve"
    
    
def edge_after_retrieve(state):
    if state.get("qdrant_data"):
        return "response"
    else: 
        return "skip_search_or_retrieve"

def edge_after_hybrid(state):
    if state.get('es_data') is None or state.get("qdrant_data") is None:
        return "response"
    else:
        return "response"