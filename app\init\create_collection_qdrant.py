import requests

# <PERSON><PERSON><PERSON> hình
QDRANT_URL = "http://127.0.0.1:6333"
COLLECTION_NAME = "collection_ssg_detail"

headers = {
    "Content-Type": "application/json"
}

# <PERSON><PERSON> liệu cấu hình collection
payload = {
    "vectors": {
        "size": 768,           # <PERSON><PERSON><PERSON> thước vector
        "distance": "Cosine"   # <PERSON><PERSON><PERSON>, Euclid, hoặc Dot
    }
}

# <PERSON><PERSON><PERSON> yêu cầu tạo collection
response = requests.put(
    f"{QDRANT_URL}/collections/{COLLECTION_NAME}",
    json=payload,
    headers=headers
)

# In kết quả
if response.status_code == 200:
    print("✅ Collection created successfully")
else:
    print(f"❌ Failed to create collection: {response.status_code}")
    print(response.text)
