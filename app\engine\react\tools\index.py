from langchain.agents import Tool
from app.engine.react.tools.es_tool import es_tool_wrapper
from app.engine.react.tools.qdrant_tool import qdrant_tool_wrapper

tools = [
    Tool(
        name="Elasticsearch",
        func=es_tool_wrapper,
        description="Search for documents by keyword using Elasticsearch.",
    # return_direct=True
    ),
    Tool(
        name="Qdrant",
        func=qdrant_tool_wrapper,
        description="Semantic search for documents using Qdrant vector similarity."
    )
]
