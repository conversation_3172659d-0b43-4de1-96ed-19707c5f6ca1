from app.engine.graph_nodes.nodes.node_search_es import node_search_es
from app.engine.graph_nodes.nodes.node_retrieve import node_retrieve
from app.engine.graph_nodes.nodes.node_keywords import node_keywords
from app.utils.index import set_global_state
import logging

logger = logging.getLogger(__name__)


def node_hybrid_retrieve_search(state):
    """
    Optimized hybrid search node - combines keyword and semantic search
    """
    logger.info("[HYBRID] Starting hybrid retrieve search")

    try:
        # 1. Extract keywords from question
        state = node_keywords(state)
        logger.info(f"[HYBRID] Keywords extracted: {state.get('keywords')}")

        # 2. Elasticsearch search with keywords
        if "keywords" in state and state["keywords"]:
            state = node_search_es(state)
            set_global_state("keywords", state["keywords"])
            logger.info(f"[HYBRID] ES search completed, found {len(state.get('es_data', []))} results")
        else:
            logger.warning("[HYBRID] No keywords for Elasticsearch search")
            state["es_data"] = []
            set_global_state("es_data", [])

        # 3. Semantic search with Qdrant
        state = node_retrieve(state)
        if "qdrant_data" in state and state["qdrant_data"]:
            set_global_state("qdrant_data", state["qdrant_data"])
            logger.info(f"[HYBRID] Qdrant search completed, found {len(state.get('qdrant_data', []))} results")
        else:
            logger.warning("[HYBRID] No qdrant_data in state")
            state["qdrant_data"] = []
            set_global_state("qdrant_data", [])

        logger.info("[HYBRID] Hybrid search completed successfully")

    except Exception as e:
        logger.error(f"[HYBRID] Error in hybrid search: {e}")
        # Set empty results on error
        state["es_data"] = []
        state["qdrant_data"] = []
        set_global_state("es_data", [])
        set_global_state("qdrant_data", [])

    return state
