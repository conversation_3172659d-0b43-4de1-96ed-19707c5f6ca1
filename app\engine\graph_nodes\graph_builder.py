from langgraph.graph import StateGraph
from typing import TypedDict

from app.engine.graph_nodes.nodes.node_classify_question import classify_question
from app.engine.graph_nodes.nodes.node_keywords import node_keywords
from app.engine.graph_nodes.nodes.node_search_es import node_search_es
from app.engine.graph_nodes.nodes.node_skip_search_or_retrieve import node_skip_search_or_retrieve
from app.engine.graph_nodes.nodes.node_response import node_response
from app.engine.graph_nodes.nodes.node_retrieve import node_retrieve
from app.engine.graph_nodes.nodes.node_hybrid_retrieve_search import node_hybrid_retrieve_search

from app.engine.graph_nodes.edges import edge_after_classify, edge_after_search, edge_after_retrieve, edge_after_hybrid


# 1. Định nghĩa schema
class GraphState(TypedDict):
    question: str
    keywords: str
    answer: str
    classification: str
    keyword_node_processed: bool
    es_data: str
    
# Khởi tạo graph builder
workflow = StateGraph(GraphState)

# 1. Entry point
workflow.add_node("classify", classify_question)
workflow.set_entry_point("classify")

# 2. Các node x<PERSON> lý tiếp theo
workflow.add_node("keyword", node_keywords)
workflow.add_node("search", node_search_es)
workflow.add_node("retrieve", node_retrieve)
workflow.add_node("hybrid", node_hybrid_retrieve_search)
workflow.add_node("skip_search_or_retrieve", node_skip_search_or_retrieve)
workflow.add_node("response", node_response)

# 3. Điều hướng sau classify
workflow.add_conditional_edges("classify", edge_after_classify, {
    "keyword": "keyword",
    "hybrid": "hybrid",
    "retrieve": "retrieve",
})

# 4. Điều hướng sau keyword → search
workflow.add_edge("keyword", "search")

# 5. Điều hướng sau search → response (có thể thêm fallback sau này)
workflow.add_conditional_edges("search", edge_after_search, {
    "response": "response",
    "retrieve": "retrieve"  
})

workflow.add_conditional_edges("retrieve", edge_after_retrieve, {
    "response": "response",
    "skip_search_or_retrieve": "skip_search_or_retrieve" 
})

workflow.add_conditional_edges("hybrid", edge_after_hybrid, {
    "response": "response",
    "skip_search_or_retrieve": "skip_search_or_retrieve" 
})

# 7. Kết thúc tại response
workflow.set_finish_point("response")
workflow.set_finish_point("skip_search_or_retrieve")

# Biên dịch graph
graph_app = workflow.compile()
