from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama import OllamaLLM
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

keyword_extraction_template = """
You are a keyword extraction module for keyword-based search.

Your task is to **extract clear, specific keywords that appear in the user's question**.
Do not create new words or infer. Only extract keywords present in the question that can be used for exact lookup or search in text.

Prioritize proper nouns, feature names, document names, or noun phrases with clear meaning.
Do not extract general, emotional, or vague words.

### Question:
{question}

### Result:
Return a list of keywords (1 to several words), separated by commas. Do not return the original question.
Example: AI Portal, Salary Policy, Chat Assistant (Must match keywords in the question)
"""


parser = StrOutputParser()
keyword_extraction_prompt = ChatPromptTemplate.from_template(keyword_extraction_template)
keywords_llm_chain = keyword_extraction_prompt | llm | parser
