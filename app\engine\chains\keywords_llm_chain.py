from langchain_core.prompts import ChatPromptTemplate
from langchain_ollama import OllamaLLM
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

keyword_extraction_template = """
Bạn là module trích xuất từ khóa cho tìm kiếm dựa trên từ khóa.

Nhiệm vụ của bạn là **trích xuất các từ khóa rõ ràng, cụ thể xuất hiện trong câu hỏi của người dùng**.
Không tạo ra từ mới hoặc suy luận. Chỉ trích xuất các từ khóa có trong câu hỏi có thể được sử dụng để tra cứu chính xác hoặc tìm kiếm trong văn bản.

Ưu tiên các danh từ riêng, tê<PERSON> t<PERSON><PERSON> nă<PERSON>, tên tà<PERSON> li<PERSON>, hoặc cụm danh từ có ý nghĩa rõ ràng.
Không trích xuất các từ chung chung, cảm xúc, hoặc mơ hồ.

### Câu hỏi:
{question}

### Kết quả:
Trả về danh sách từ khóa (1 đến nhiều từ), phân cách bằng dấu phẩy. Không trả về câu hỏi gốc.
Ví dụ: AI Portal, Chính sách lương, Trợ lý chat (Phải khớp với từ khóa trong câu hỏi)
"""


parser = StrOutputParser()
keyword_extraction_prompt = ChatPromptTemplate.from_template(keyword_extraction_template)
keywords_llm_chain = keyword_extraction_prompt | llm | parser
