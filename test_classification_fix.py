#!/usr/bin/env python3
"""
Test script for classification fix
"""
import sys
import os

# Add the current directory to Python path
sys.path.append('.')

from app.engine.chains.classification_llm_chain import classification_llm_chain
from app.engine.graph_nodes.edges import edge_after_classify

def test_classification_chain():
    """Test classification chain directly"""
    print("=" * 60)
    print("Testing Classification Chain")
    print("=" * 60)
    
    test_questions = [
        "Sao Mai Solution Group cung cấp dịch vụ gì?",
        "Làm thế nào để reset mật khẩu?",
        "Tại sao nên sử dụng AI?",
        "",  # Empty string
        "   ",  # Whitespace only
        "Hello world",  # Simple English
    ]
    
    for question in test_questions:
        print(f"\nCâu hỏi: '{question}'")
        try:
            result = classification_llm_chain.invoke({"question": question})
            print(f"Raw result: '{result}'")
            
            # Clean result like in the actual code
            classification_clean = result.strip().lower()
            print(f"Cleaned result: '{classification_clean}'")
            
            # Validate
            valid_classifications = ["keyword", "semantic", "hybrid"]
            if classification_clean not in valid_classifications:
                print(f"❌ Invalid classification: '{classification_clean}'")
            else:
                print(f"✅ Valid classification: '{classification_clean}'")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 40)

def test_edge_function():
    """Test edge function with various states"""
    print("\n" + "=" * 60)
    print("Testing Edge Function")
    print("=" * 60)
    
    test_states = [
        {"classification": "keyword"},
        {"classification": "semantic"},
        {"classification": "hybrid"},
        {"classification": "invalid"},
        {"classification": None},
        {"classification": ""},
        {},  # Empty state
    ]
    
    for state in test_states:
        classification = state.get('classification')
        print(f"\nState: classification = '{classification}'")
        
        try:
            result = edge_after_classify(state)
            print(f"Edge result: '{result}'")
            
            # Validate edge result
            valid_edges = ["keyword", "hybrid", "retrieve"]
            if result in valid_edges:
                print(f"✅ Valid edge: '{result}'")
            else:
                print(f"❌ Invalid edge: '{result}'")
                
        except Exception as e:
            print(f"❌ Edge error: {e}")
        
        print("-" * 40)

def test_full_classification_flow():
    """Test the full classification flow"""
    print("\n" + "=" * 60)
    print("Testing Full Classification Flow")
    print("=" * 60)
    
    test_question = "Sao Mai Solution Group có những sản phẩm nào?"
    
    print(f"Test question: '{test_question}'")
    
    try:
        # Step 1: Classification
        print("\n1. Classification step:")
        classification_result = classification_llm_chain.invoke({"question": test_question})
        print(f"Raw classification: '{classification_result}'")
        
        # Step 2: Clean and validate
        print("\n2. Clean and validate:")
        classification_clean = classification_result.strip().lower()
        print(f"Cleaned: '{classification_clean}'")
        
        valid_classifications = ["keyword", "semantic", "hybrid"]
        if classification_clean not in valid_classifications:
            print(f"Invalid, defaulting to 'hybrid'")
            classification_clean = "hybrid"
        
        print(f"Final classification: '{classification_clean}'")
        
        # Step 3: Edge routing
        print("\n3. Edge routing:")
        state = {"classification": classification_clean}
        edge_result = edge_after_classify(state)
        print(f"Edge result: '{edge_result}'")
        
        print("\n✅ Full flow completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Full flow error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🔧 Classification Fix Test Suite")
    print("=" * 60)
    
    # Test 1: Classification chain
    test_classification_chain()
    
    # Test 2: Edge function
    test_edge_function()
    
    # Test 3: Full flow
    test_full_classification_flow()
    
    print("\n" + "=" * 60)
    print("📋 Fix Summary")
    print("=" * 60)
    print("Issues Fixed:")
    print("✅ Added default return in edge_after_classify()")
    print("✅ Added validation in node_classify_question.py")
    print("✅ Added logging for debugging")
    print("✅ Improved error handling")
    
    print("\nExpected Results:")
    print("• No more KeyError: None in graph execution")
    print("• Classification always returns valid values")
    print("• Better error logging for debugging")
    print("• Graceful fallback to 'hybrid' classification")

if __name__ == "__main__":
    main()
