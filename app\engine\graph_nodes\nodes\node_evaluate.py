# def node_judge_answer(state):
#     question = state["question"]
#     qdrant_data = state.get("qdrant_data")
#     es_data = state.get("es_data")
#     answer = state["response"]
    
#     prompt = f"""
#     C<PERSON><PERSON> hỏi: {question}
#     <PERSON><PERSON> cảnh: {context}
#     <PERSON><PERSON><PERSON> trả lời: {answer}
    
#     H<PERSON>y đánh giá xem câu trả lời có phù hợp với câu hỏi và ngữ cảnh hay không?
#     Trả lời dưới dạng: accept / reject, và lý do ngắn gọn.
#     """

#     eval_result = llm.invoke(prompt)
    
#     if "accept" in eval_result.lower():
#         state["judge_result"] = "accept"
#     else:
#         state["judge_result"] = "reject"
#         state["judge_reason"] = eval_result
    
#     return state
