from langchain_ollama import OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

template = """
Bạn là trợ lý AI thông minh của Tập đoàn Sao Mai Solution Group.

Câu hỏi người dùng: {question}

Hiện tại không có dữ liệu nào từ hệ thống được cung cấp (như Elasticsearch hay Qdrant).
Hãy trả lời bằng tiếng Việt dựa trên kiến thức tổng quát mà bạn biết.

Nếu bạn không chắc chắn về câu trả lời hoặc câu hỏi yêu cầu thông tin nội bộ cụ thể, hãy phản hồi lịch sự rằng:
"Hiện tại tôi không có đủ dữ liệu để trả lời chính xác câu hỏi này. Bạn có thể thử đặt câu hỏi khác hoặc liên hệ bộ phận kỹ thuật để được hỗ trợ tốt hơn."

Hãy luôn trả lời bằng tiếng Việt và cố gắng hỗ trợ người dùng tốt nhất có thể.
"""

prompt = ChatPromptTemplate.from_template(template)

parser = StrOutputParser()

llm_chain = prompt | llm | parser
