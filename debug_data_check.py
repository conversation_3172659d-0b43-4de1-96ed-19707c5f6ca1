"""
Script để kiểm tra dữ liệu trong Elasticsearch và Qdrant
"""
import requests
import json
from app.config.index import settings
from app.utils.qdrant_simple import get_qdrant_client_simple, health_check_qdrant

def check_elasticsearch_data():
    """Kiểm tra dữ liệu trong Elasticsearch"""
    print("=== CHECKING ELASTICSEARCH ===")
    
    # Check cluster health
    try:
        response = requests.get(f"{settings.ES_URL}/_cluster/health")
        print(f"ES Cluster Health: {response.json()}")
    except Exception as e:
        print(f"ES Health check failed: {e}")
        return
    
    # Check index exists
    try:
        response = requests.get(f"{settings.ES_URL}/{settings.ES_INDEX}")
        if response.status_code == 200:
            index_info = response.json()
            print(f"Index '{settings.ES_INDEX}' exists")
            print(f"Index settings: {json.dumps(index_info, indent=2)}")
        else:
            print(f"Index '{settings.ES_INDEX}' not found: {response.status_code}")
            return
    except Exception as e:
        print(f"Index check failed: {e}")
        return
    
    # Check document count
    try:
        response = requests.get(f"{settings.ES_URL}/{settings.ES_INDEX}/_count")
        count_info = response.json()
        print(f"Document count: {count_info.get('count', 0)}")
    except Exception as e:
        print(f"Count check failed: {e}")
    
    # Sample search
    try:
        search_query = {
            "query": {"match_all": {}},
            "size": 3
        }
        response = requests.post(f"{settings.ES_URL}/{settings.ES_INDEX}/_search", json=search_query)
        if response.status_code == 200:
            results = response.json()
            hits = results.get('hits', {}).get('hits', [])
            print(f"Sample documents ({len(hits)}):")
            for i, hit in enumerate(hits):
                print(f"  Doc {i+1}: {json.dumps(hit['_source'], indent=4, ensure_ascii=False)}")
        else:
            print(f"Sample search failed: {response.status_code}")
    except Exception as e:
        print(f"Sample search failed: {e}")
    
    # Search for camera-related content
    try:
        camera_query = {
            "query": {
                "multi_match": {
                    "query": "camera quản lý saomaisoft",
                    "fields": ["keyword", "text", "content"]
                }
            },
            "size": 5
        }
        response = requests.post(f"{settings.ES_URL}/{settings.ES_INDEX}/_search", json=camera_query)
        if response.status_code == 200:
            results = response.json()
            hits = results.get('hits', {}).get('hits', [])
            print(f"Camera-related documents ({len(hits)}):")
            for i, hit in enumerate(hits):
                source = hit['_source']
                print(f"  Doc {i+1}:")
                print(f"    Score: {hit['_score']}")
                for key, value in source.items():
                    if isinstance(value, str):
                        preview = value[:100] + "..." if len(value) > 100 else value
                        print(f"    {key}: {preview}")
                    else:
                        print(f"    {key}: {value}")
                print()
        else:
            print(f"Camera search failed: {response.status_code}")
    except Exception as e:
        print(f"Camera search failed: {e}")


def check_qdrant_data():
    """Kiểm tra dữ liệu trong Qdrant"""
    print("\n=== CHECKING QDRANT ===")
    
    # Health check
    is_healthy = health_check_qdrant()
    print(f"Qdrant healthy: {is_healthy}")
    
    if not is_healthy:
        return
    
    try:
        client = get_qdrant_client_simple()
        
        # Get collection info
        collection_info = client.get_collection(settings.QDRANT_COLLECTION)
        print(f"Collection info:")
        print(f"  Status: {collection_info.status}")
        print(f"  Points count: {collection_info.points_count}")
        print(f"  Vector size: {collection_info.config.params.vectors.size}")
        
        # Sample points
        points = client.scroll(
            collection_name=settings.QDRANT_COLLECTION,
            limit=3,
            with_payload=True
        )
        
        print(f"Sample points ({len(points[0])}):")
        for i, point in enumerate(points[0]):
            print(f"  Point {i+1}:")
            print(f"    ID: {point.id}")
            if point.payload:
                for key, value in point.payload.items():
                    if isinstance(value, str):
                        preview = value[:100] + "..." if len(value) > 100 else value
                        print(f"    {key}: {preview}")
                    else:
                        print(f"    {key}: {value}")
            print()
            
    except Exception as e:
        print(f"Qdrant data check failed: {e}")


def test_search_pipeline():
    """Test toàn bộ search pipeline"""
    print("\n=== TESTING SEARCH PIPELINE ===")
    
    test_queries = [
        "camera",
        "quản lý camera",
        "saomaisoft",
        "hệ thống camera saomaisoft"
    ]
    
    for query in test_queries:
        print(f"\nTesting query: '{query}'")
        
        # Test ES search
        try:
            from app.utils.elasticsearch_optimized import search_elasticsearch_simple
            es_results = search_elasticsearch_simple(query, settings.ES_INDEX, settings.ES_URL)
            print(f"  ES results: {len(es_results)}")
            if es_results:
                print(f"  First ES result keys: {list(es_results[0].keys())}")
        except Exception as e:
            print(f"  ES search failed: {e}")
        
        # Test Qdrant search
        try:
            from app.utils.qdrant_simple import search_qdrant_simple
            qdrant_results = search_qdrant_simple(query)
            print(f"  Qdrant results: {len(qdrant_results)}")
            if qdrant_results:
                print(f"  First Qdrant result type: {type(qdrant_results[0])}")
        except Exception as e:
            print(f"  Qdrant search failed: {e}")


if __name__ == "__main__":
    print("Starting data check...")
    check_elasticsearch_data()
    check_qdrant_data()
    test_search_pipeline()
    print("\nData check completed!")
