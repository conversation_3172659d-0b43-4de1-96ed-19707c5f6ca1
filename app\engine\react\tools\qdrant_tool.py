from app.utils.index import get_global_state

def get_qdrant_retrieve_data() -> str:
    """
    Retrieve data from Qdrant saved in global_state with key 'qdrant_data'.
    """
    global_state = get_global_state()
    # print("[QDRANT TOOL] Reading global_state id:", id(global_state))
    try:
        if "qdrant_data" not in global_state:
            raise ValueError("Key 'qdrant_data' does not exist in global_state")

        docs_qdrant = global_state["qdrant_data"]
        # print("[DEBUG] docs_qdrant: ", docs_qdrant)

        # Assign content to variable before return
        result_text = "\n\n".join([
            f"Content: {doc.page_content}\nMetadata: {doc.metadata}" for doc in docs_qdrant
        ])
        print("[DEBUG] docs_qdrant: ", result_text)
        if not result_text:
            result_text = "No data found in Qdrant."

        return result_text

    except Exception as e:
        return f"[get_qdrant_retrieve_data] Error: {e}"

def qdrant_tool_wrapper(query) -> str:
    """
    Semantic search result viewer for documents from Qdrant.
    Input is ignored – this just returns the latest retrieved content.
    """
    try:
        print(f"[DEBUG] Qdrant tool received input: {query} type: {type(query)}")
        return get_qdrant_retrieve_data()
    except Exception as e:
        return f"Error processing Qdrant data: {e}"
