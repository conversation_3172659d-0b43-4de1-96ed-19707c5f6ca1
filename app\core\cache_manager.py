"""
Cache Manager - Redis-like caching cho search results và AI responses
"""
import hashlib
import json
import asyncio
from typing import Any, Optional, Dict
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class InMemoryCache:
    """In-memory cache với TTL support"""
    
    def __init__(self, default_ttl: int = 3600):  # 1 hour default
        self._cache: Dict[str, Dict] = {}
        self._default_ttl = default_ttl
        self._lock = asyncio.Lock()
    
    def _generate_key(self, prefix: str, data: Any) -> str:
        """Tạo cache key từ data"""
        if isinstance(data, dict):
            data_str = json.dumps(data, sort_keys=True)
        else:
            data_str = str(data)
        
        hash_obj = hashlib.md5(data_str.encode())
        return f"{prefix}:{hash_obj.hexdigest()}"
    
    def _is_expired(self, item: Dict) -> bool:
        """Kiểm tra item có expired không"""
        return datetime.now() > item['expires_at']
    
    async def get(self, key: str) -> Optional[Any]:
        """Lấy value từ cache"""
        async with self._lock:
            if key in self._cache:
                item = self._cache[key]
                if not self._is_expired(item):
                    logger.debug(f"Cache hit for key: {key}")
                    return item['value']
                else:
                    # Remove expired item
                    del self._cache[key]
                    logger.debug(f"Cache expired for key: {key}")
            
            logger.debug(f"Cache miss for key: {key}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Lưu value vào cache"""
        ttl = ttl or self._default_ttl
        expires_at = datetime.now() + timedelta(seconds=ttl)
        
        async with self._lock:
            self._cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': datetime.now()
            }
            logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
    
    async def delete(self, key: str) -> None:
        """Xóa key khỏi cache"""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"Cache deleted for key: {key}")
    
    async def clear(self) -> None:
        """Xóa toàn bộ cache"""
        async with self._lock:
            self._cache.clear()
            logger.info("Cache cleared")
    
    async def cleanup_expired(self) -> int:
        """Dọn dẹp các items đã expired"""
        expired_keys = []
        async with self._lock:
            for key, item in self._cache.items():
                if self._is_expired(item):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
        
        logger.info(f"Cleaned up {len(expired_keys)} expired cache items")
        return len(expired_keys)
    
    def get_stats(self) -> Dict:
        """Lấy thống kê cache"""
        return {
            'total_items': len(self._cache),
            'memory_usage_mb': sum(
                len(str(item)) for item in self._cache.values()
            ) / (1024 * 1024)
        }


class CacheManager:
    """Manager cho các loại cache khác nhau"""

    def __init__(self, app_settings=None):
        # Get settings or use defaults
        if app_settings:
            search_ttl = app_settings.CACHE_TTL_SEARCH
            ai_ttl = app_settings.CACHE_TTL_AI
            embedding_ttl = app_settings.CACHE_TTL_EMBEDDING
            classification_ttl = app_settings.CACHE_TTL_CLASSIFICATION
        else:
            # Default values
            search_ttl = 3600
            ai_ttl = 1800
            embedding_ttl = 86400
            classification_ttl = 21600

        # Cache cho search results
        self.search_cache = InMemoryCache(default_ttl=search_ttl)

        # Cache cho AI responses
        self.ai_cache = InMemoryCache(default_ttl=ai_ttl)

        # Cache cho embeddings
        self.embedding_cache = InMemoryCache(default_ttl=embedding_ttl)

        # Cache cho classification results
        self.classification_cache = InMemoryCache(default_ttl=classification_ttl)
    
    async def get_search_result(self, query: str, search_type: str) -> Optional[Any]:
        """Lấy kết quả search từ cache"""
        key = self.search_cache._generate_key(f"search_{search_type}", query)
        return await self.search_cache.get(key)
    
    async def set_search_result(self, query: str, search_type: str, result: Any, ttl: Optional[int] = None) -> None:
        """Lưu kết quả search vào cache"""
        key = self.search_cache._generate_key(f"search_{search_type}", query)
        await self.search_cache.set(key, result, ttl)
    
    async def get_ai_response(self, prompt: str, model: str) -> Optional[Any]:
        """Lấy AI response từ cache"""
        key = self.ai_cache._generate_key(f"ai_{model}", prompt)
        return await self.ai_cache.get(key)
    
    async def set_ai_response(self, prompt: str, model: str, response: Any, ttl: Optional[int] = None) -> None:
        """Lưu AI response vào cache"""
        key = self.ai_cache._generate_key(f"ai_{model}", prompt)
        await self.ai_cache.set(key, response, ttl)
    
    async def get_embedding(self, text: str) -> Optional[Any]:
        """Lấy embedding từ cache"""
        key = self.embedding_cache._generate_key("embedding", text)
        return await self.embedding_cache.get(key)
    
    async def set_embedding(self, text: str, embedding: Any, ttl: Optional[int] = None) -> None:
        """Lưu embedding vào cache"""
        key = self.embedding_cache._generate_key("embedding", text)
        await self.embedding_cache.set(key, embedding, ttl)
    
    async def get_classification(self, question: str) -> Optional[str]:
        """Lấy classification result từ cache"""
        key = self.classification_cache._generate_key("classification", question)
        return await self.classification_cache.get(key)
    
    async def set_classification(self, question: str, classification: str, ttl: Optional[int] = None) -> None:
        """Lưu classification result vào cache"""
        key = self.classification_cache._generate_key("classification", question)
        await self.classification_cache.set(key, classification, ttl)
    
    async def cleanup_all(self) -> Dict[str, int]:
        """Dọn dẹp tất cả cache"""
        results = {}
        results['search'] = await self.search_cache.cleanup_expired()
        results['ai'] = await self.ai_cache.cleanup_expired()
        results['embedding'] = await self.embedding_cache.cleanup_expired()
        results['classification'] = await self.classification_cache.cleanup_expired()
        return results
    
    def get_all_stats(self) -> Dict:
        """Lấy thống kê tất cả cache"""
        return {
            'search_cache': self.search_cache.get_stats(),
            'ai_cache': self.ai_cache.get_stats(),
            'embedding_cache': self.embedding_cache.get_stats(),
            'classification_cache': self.classification_cache.get_stats(),
        }


# Global cache manager (will be initialized in main.py with proper settings)
cache_manager = None

def initialize_cache_manager(app_settings=None):
    """Initialize cache manager with environment-specific settings"""
    global cache_manager
    cache_manager = CacheManager(app_settings)
    return cache_manager


# Sync wrapper functions for use in sync contexts
def get_classification_sync(question: str) -> Optional[str]:
    """Sync wrapper for get_classification"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # We're in an async context, can't use asyncio.run()
            return None  # Skip cache in this case
        else:
            return asyncio.run(cache_manager.get_classification(question))
    except Exception:
        return None


def set_classification_sync(question: str, classification: str, ttl: Optional[int] = None) -> None:
    """Sync wrapper for set_classification"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # We're in an async context, can't use asyncio.run()
            return  # Skip cache in this case
        else:
            asyncio.run(cache_manager.set_classification(question, classification, ttl))
    except Exception:
        pass


def get_search_result_sync(query: str, search_type: str) -> Optional[Any]:
    """Sync wrapper for get_search_result"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return None  # Skip cache in this case
        else:
            return asyncio.run(cache_manager.get_search_result(query, search_type))
    except Exception:
        return None


def set_search_result_sync(query: str, search_type: str, result: Any, ttl: Optional[int] = None) -> None:
    """Sync wrapper for set_search_result"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return  # Skip cache in this case
        else:
            asyncio.run(cache_manager.set_search_result(query, search_type, result, ttl))
    except Exception:
        pass
