"""
Optimized Elasticsearch utilities với caching và connection pooling
"""
import requests
from app.config.index import settings
from app.core.vector_manager import get_es_session
from app.core.cache_manager import cache_manager
import logging
from typing import List, Dict, Any, Optional
import asyncio

logger = logging.getLogger(__name__)


def build_es_query(query_text: str) -> Dict[str, Any]:
    """
    Build Elasticsearch query - tối ưu từ code gốc
    """
    must_clauses = []
    
    # Full-text search trên title + review
    must_clauses.append({
        "multi_match": {
            "query": query_text,
            "fields": ["keyword", "text"],
            "type": "best_fields",
            "fuzziness": "AUTO",
            "operator": "or"
        }
    })
    
    return {
        "query": {
            "bool": {
                "must": must_clauses
            }
        },
        "size": 10,  # Limit results
        # "_source": ["keyword", "text", "metadata"],  # Return all fields to debug
        "timeout": "30s"  # Add timeout
    }


async def search_elasticsearch_optimized(
    query_text: str, 
    index_name: str, 
    es_url: Optional[str] = None,
    use_cache: bool = True
) -> List[Dict[str, Any]]:
    """
    Optimized Elasticsearch search với caching và connection pooling
    """
    es_url = es_url or settings.ES_URL
    
    # Check cache first
    if use_cache:
        cache_key = f"{query_text}_{index_name}"
        cached_result = await cache_manager.get_search_result(cache_key, "elasticsearch")
        if cached_result:
            logger.info(f"Cache hit for ES search: {query_text[:50]}...")
            return cached_result
    
    try:
        # Get optimized session
        session = await get_es_session()
        
        # Build query
        url = f"{es_url}/{index_name}/_search"
        body = build_es_query(query_text)
        
        # Perform search
        logger.info(f"Performing ES search for: {query_text[:50]}...")
        response = session.post(url, json=body, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"Elasticsearch search failed: {response.status_code} - {response.text}")
            return []
        
        results = response.json()
        hits = results.get("hits", {}).get("hits", [])
        
        # Cache the result
        if use_cache:
            await cache_manager.set_search_result(cache_key, "elasticsearch", hits, ttl=3600)
        
        logger.info(f"ES search completed, found {len(hits)} documents")
        return hits
        
    except requests.exceptions.Timeout:
        logger.error(f"Elasticsearch search timeout for query: {query_text}")
        return []
    except requests.exceptions.ConnectionError:
        logger.error(f"Elasticsearch connection error for query: {query_text}")
        return []
    except Exception as e:
        logger.error(f"Elasticsearch search error: {e}")
        return []


def search_elasticsearch_sync(
    query_text: str, 
    index_name: str, 
    es_url: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Synchronous version for backward compatibility
    """
    try:
        # Run async function in sync context
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, create a new task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    asyncio.run, 
                    search_elasticsearch_optimized(query_text, index_name, es_url)
                )
                return future.result(timeout=60)
        else:
            return asyncio.run(search_elasticsearch_optimized(query_text, index_name, es_url))
    except Exception as e:
        logger.error(f"Sync ES search error: {e}")
        return []


async def bulk_search_elasticsearch(
    queries: List[str], 
    index_name: str, 
    es_url: Optional[str] = None
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Bulk search for multiple queries
    """
    results = {}
    
    # Use semaphore to limit concurrent requests
    semaphore = asyncio.Semaphore(5)  # Max 5 concurrent requests
    
    async def search_single(query: str):
        async with semaphore:
            return await search_elasticsearch_optimized(query, index_name, es_url)
    
    # Execute all searches concurrently
    tasks = [search_single(query) for query in queries]
    search_results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Combine results
    for query, result in zip(queries, search_results):
        if isinstance(result, Exception):
            logger.error(f"Bulk search error for query '{query}': {result}")
            results[query] = []
        else:
            results[query] = result
    
    return results


async def health_check_elasticsearch(es_url: Optional[str] = None) -> bool:
    """
    Check Elasticsearch health
    """
    es_url = es_url or settings.ES_URL

    try:
        session = await get_es_session()
        response = session.get(f"{es_url}/_cluster/health", timeout=10)

        if response.status_code == 200:
            health_data = response.json()
            status = health_data.get("status", "red")
            logger.info(f"Elasticsearch health: {status}")
            return status in ["green", "yellow"]
        else:
            logger.error(f"Elasticsearch health check failed: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"Elasticsearch health check error: {e}")
        return False


def health_check_elasticsearch_simple(es_url: Optional[str] = None) -> bool:
    """
    Simple sync Elasticsearch health check
    """
    es_url = es_url or settings.ES_URL

    try:
        response = requests.get(f"{es_url}/_cluster/health", timeout=10)

        if response.status_code == 200:
            health_data = response.json()
            status = health_data.get("status", "red")
            logger.info(f"Elasticsearch health: {status}")
            return status in ["green", "yellow"]
        else:
            logger.error(f"Elasticsearch health check failed: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"Elasticsearch health check error: {e}")
        return False


def search_elasticsearch_simple(
    query_text: str,
    index_name: str,
    es_url: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Simple sync Elasticsearch search without async dependencies
    """
    es_url = es_url or settings.ES_URL

    try:
        # Use simple requests without session pooling for sync context
        url = f"{es_url}/{index_name}/_search"
        body = build_es_query(query_text)

        logger.info(f"Performing simple ES search for: {query_text[:50]}...")
        response = requests.post(url, json=body, timeout=30)

        if response.status_code != 200:
            logger.error(f"Elasticsearch search failed: {response.status_code} - {response.text}")
            return []

        results = response.json()
        hits = results.get("hits", {}).get("hits", [])

        logger.info(f"Simple ES search completed, found {len(hits)} documents")
        return hits

    except requests.exceptions.Timeout:
        logger.error(f"Elasticsearch search timeout for query: {query_text}")
        return []
    except requests.exceptions.ConnectionError:
        logger.error(f"Elasticsearch connection error for query: {query_text}")
        return []
    except Exception as e:
        logger.error(f"Elasticsearch search error: {e}")
        return []


# Backward compatibility - wrapper cho function gốc
def search_elasticsearch(query_text: str, index_name: str, es_url: str = "http://localhost:9200"):
    """
    Backward compatibility wrapper
    """
    return search_elasticsearch_simple(query_text, index_name, es_url)
