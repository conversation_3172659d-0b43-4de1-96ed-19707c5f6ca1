from app.utils.index import set_global_state
from app.utils.elasticsearch_optimized import search_elasticsearch_simple
from app.config.index import settings
import logging

logger = logging.getLogger(__name__)


def node_search_es(state):
    """
    Optimized Elasticsearch search node v<PERSON><PERSON> caching
    """
    keywords = state.get("keywords", "")
    if not keywords:
        logger.info("[ES] No keywords for search")
        state["es_data"] = []
        set_global_state("es_data", [])
        return state

    try:
        # Perform simple sync search
        logger.info(f"[ES] Searching for keywords: {keywords}")
        es_data = search_elasticsearch_simple(
            keywords,
            settings.ES_INDEX,
            settings.ES_URL
        )

        print(f"[ES DEBUG] Raw es_data: {es_data}")

        if es_data:
            state["es_data"] = es_data
            set_global_state("es_data", es_data)
            logger.info(f"[ES] Found {len(es_data)} results for: {keywords}")

            # Print first item structure
            if len(es_data) > 0:
                if isinstance(es_data[0], dict) and '_source' in es_data[0]:
                    if 'metadata' in es_data[0]['_source']:
                        print(f"[ES DEBUG] First item metadata: {es_data[0]['_source']['metadata']}")
                        print(f"[ES DEBUG] Metadata type: {type(es_data[0]['_source']['metadata'])}")

        else:
            print("[ES DEBUG] No es_data found")
            logger.info(f"[ES] No results found from Elasticsearch for: {keywords}")
            state["es_data"] = []
            set_global_state("es_data", [])

    except Exception as e:
        logger.error(f"[ES] Search error: {e}")
        state["es_data"] = []
        set_global_state("es_data", [])

    return state

