# ===========================================
# PRODUCTION ENVIRONMENT SETTINGS
# ===========================================

# Database & External Services (cập nhật cho production)
MONGO_URI=mongodb://your-production-mongo:27017
DATABASE=chatppyt
API_KEY=your-production-gemini-api-key

# CORS Origins (cập nhật cho production)
ORIGIN_PORTAL=https://your-production-portal.com
ORIGIN_PORTAL_IP=http://your-production-ip:6868
ORIGIN_PORTAL_DOCKER=http://your-production-docker:5173
ORIGIN_KONG=https://your-production-kong.com
ORIGIN_KONG_IP=http://your-production-kong-ip:8000

# AI Services (cập nhật cho production)
OLLAMA_URL=http://your-production-ollama:11434
HOST_QDRANT=your-production-qdrant-host
ES_URL=http://your-production-elasticsearch:9200

# Collections
QDRANT_COLLECTION=collection_ssg_detail
ES_INDEX=index_ssg_detail

# ===========================================
# PRODUCTION PERFORMANCE SETTINGS
# ===========================================

# Application Settings
ENVIRONMENT=production
WORKERS=4
MAX_CONNECTIONS=100
KEEP_ALIVE=2

# Cache TTL Settings (longer for production)
CACHE_TTL_SEARCH=3600      # 1 hour
CACHE_TTL_AI=1800          # 30 minutes
CACHE_TTL_EMBEDDING=86400  # 24 hours
CACHE_TTL_CLASSIFICATION=21600  # 6 hours

# Database Pool Settings (larger for production)
DB_POOL_SIZE=50
DB_MIN_POOL_SIZE=10
DB_MAX_IDLE_TIME=30000
DB_SERVER_SELECTION_TIMEOUT=5000
DB_CONNECT_TIMEOUT=10000
DB_SOCKET_TIMEOUT=20000

# Vector Store Settings
QDRANT_TIMEOUT=30
ES_MAX_RETRIES=3
ES_TIMEOUT=30
ES_MAX_TIMEOUT=60

# LLM Settings (more focused for production)
LLM_TEMPERATURE=0.1
LLM_NUM_PREDICT=512
LLM_TOP_K=10
LLM_TOP_P=0.9

# Search Settings
SEARCH_K=2
SEARCH_FETCH_K=20
SEARCH_LAMBDA_MULT=0.7

# Monitoring Settings
SLOW_REQUEST_THRESHOLD=1.0
LOG_LEVEL=INFO
