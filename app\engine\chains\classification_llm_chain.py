from langchain_ollama import OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

# classification_template = """
# You are a question classification module.

# Classify the following question into one of three categories. Respond with **exactly one word**: "keyword", "semantic", or "hybrid".

# Definitions:
# - "keyword": The question contains clear, specific keywords that can be matched directly (e.g., "reset Windows password", "API endpoint for user login").
# - "semantic": The question involves descriptions, emotions, intent, or requires understanding context or meaning (e.g., "Why is microservices architecture preferred in large systems?", "How does our company culture support innovation?").
# - "hybrid": The question is ambiguous, combines both keyword-based and semantic aspects, or you are unsure.

# Examples:
# Q: "<PERSON>àm thế nào để cấu hình VPN trên máy tính xách tay của công ty?"
# A: keyword

# Q: "Tại sao chúng ta nên di chuyể<PERSON> sang cơ sở hạ tầng đám mây?"
# A: semantic

# Q: "Các phương pháp hay nhất để quản lý dự án trong CNTT?"
# A: Hybrid

# ### Question:
# {question}

# ### Answer (respond with exactly one word: keyword, semantic, or hybrid):
# """

classification_template = """
You are a question classification module.

Classify the following question into one of three categories. Respond with **exactly one word**: "keyword", "semantic", or "hybrid".

Definitions:
- "keyword": Only use this if the question includes clear and unambiguous technical terms or instructions that can be matched 1:1 with documentation, commands, or known steps. This category is rare.
- "semantic": Only use this if the question involves clear emotional tone, open-ended reasoning, or abstract language without direct action items.
- "hybrid": Use this for most real-world questions — especially if the question is a mix, ambiguous, or you're not absolutely certain. If there's any doubt, choose "hybrid".

**IMPORTANT: Assume that 90% of real-world questions are best classified as "hybrid". When uncertain, default to "hybrid".**

Examples:
Q: "How to configure VPN on company laptop?"
A: hybrid

Q: "Why should we migrate to cloud infrastructure?"
A: hybrid

Q: "What are the best practices for IT project management?"
A: hybrid

Q: "Where to change company email password?"
A: hybrid

Q: "What are your feelings about the current work environment?"
A: semantic

Q: "What is the command to delete a local Git branch?"
A: keyword

### Question:
{question}

### Answer (respond with exactly one word: keyword, semantic, or hybrid):
"""


classification_prompt = ChatPromptTemplate.from_template(classification_template)

parser = StrOutputParser()

classification_llm_chain = classification_prompt | llm | parser