from langchain_ollama import OllamaLLM
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)



classification_template = """
Bạn là module phân loại câu hỏi.

Hãy phân loại câu hỏi sau đây vào một trong ba danh mục. Trả lời bằng **chính xác một từ**: "keyword", "semantic", hoặc "hybrid".

Định nghĩa:
- "keyword": Chỉ sử dụng khi câu hỏi bao gồm các thuật ngữ kỹ thuật rõ ràng và không mơ hồ hoặc hướng dẫn có thể được khớp 1:1 với tài liệu, l<PERSON><PERSON>, hoặc các bước đã biết. <PERSON><PERSON> mục này hiếm khi được sử dụng.
- "semantic": Chỉ sử dụng khi câu hỏi có tông cảm xú<PERSON> rõ ràng, lý luận mở, hoặc ngôn ngữ trừu tượng không có các hành động cụ thể.
- "hybrid": Sử dụng cho hầu hết các câu hỏi thực tế — đặc biệt nếu câu hỏi là hỗn hợp, mơ hồ, hoặc bạn không hoàn toàn chắc chắn. Nếu có bất kỳ nghi ngờ nào, hãy chọn "hybrid".

**QUAN TRỌNG: Giả định rằng 90% câu hỏi thực tế được phân loại tốt nhất là "hybrid". Khi không chắc chắn, mặc định chọn "hybrid".**

Ví dụ:
Q: "Làm thế nào để cấu hình VPN trên laptop công ty?"
A: hybrid

Q: "Tại sao chúng ta nên di chuyển sang hạ tầng đám mây?"
A: hybrid

Q: "Các phương pháp hay nhất cho quản lý dự án CNTT là gì?"
A: hybrid

Q: "Ở đâu để thay đổi mật khẩu email công ty?"
A: hybrid

Q: "Cảm nhận của bạn về môi trường làm việc hiện tại như thế nào?"
A: semantic

Q: "Lệnh để xóa một nhánh Git local là gì?"
A: keyword

### Câu hỏi:
{question}

### Trả lời (trả lời bằng chính xác một từ: keyword, semantic, hoặc hybrid):
"""


classification_prompt = ChatPromptTemplate.from_template(classification_template)

parser = StrOutputParser()

classification_llm_chain = classification_prompt | llm | parser