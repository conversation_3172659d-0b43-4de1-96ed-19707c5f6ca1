from bson import ObjectId
import requests
import dateparser
from langchain_ollama import OllamaEmbeddings
from app.config.index import settings


# Hàm chuyển ObjectId thành chuỗi
def convert_objectid_to_str(data):
    if isinstance(data, dict):
        return {key: convert_objectid_to_str(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_objectid_to_str(item) for item in data]
    elif isinstance(data, ObjectId):
        return str(data)
    return data


def search_elasticsearch(query_text: str, index_name: str, es_url: str="http://localhost:9200"):
    url = f"{es_url}/{index_name}/_search"
    
    body = build_es_query(query_text)

    response = requests.post(url, json=body)

    if response.status_code != 200:
        raise Exception(f"Elasticsearch search failed: {response.text}")

    results = response.json()
    hits = results.get("hits", {}).get("hits", [])
    return hits


def parse_any_date(date_str):
    dt = dateparser.parse(date_str)
    if dt:
        return dt.strftime("%Y-%m-%d")
    else:
        raise ValueError(f"Không thể hiểu ngày: {date_str}")


def build_es_query(parsed):
    # keywords = parsed.get("keywords", [])
    # rating_filter = parsed.get("rating", {})
    # date_filter = parsed.get("date_range", {})

    must_clauses = []

    # Full-text search trên title + review
    # if keywords:
    if True: 
        must_clauses.append({
            "multi_match": {
                # "query": " ".join(keywords),
                "query": parsed,
                "fields": ["keyword", "text"]
            }
        })

    # Lọc theo rating
    # if rating_filter:
    #     if isinstance(rating_filter, dict) and any(k in rating_filter for k in ["gte", "lte", "gt", "lt"]):
    #         filter_clauses.append({
    #             "range": {
    #                 "rating": rating_filter
    #             }
    #         })


    # # Lọc theo date range
    # if date_filter:
        # formatted_date_filter = {}
        # for bound in ["gte", "lte"]:
        #     if bound in date_filter:
        #         try:
        #             formatted_date_filter[bound] = parse_any_date(date_filter[bound])
        #         except ValueError as e:
        #             print(e)
        # if formatted_date_filter:
        #     formatted_date_filter["format"] = "yyyy-MM-dd"
        #     filter_clauses.append({
        #         "range": {
        #             "date": formatted_date_filter
        #         }
        #     })

    return {
        "query": {
            "bool": {
                "must": must_clauses,
                # "filter": filter_clauses
            }
        }
    }

# Khởi tạo instance một lần
embeddings = OllamaEmbeddings(model="nomic-embed-text:latest", base_url=settings.OLLAMA_URL)

# Hàm tạo vector embedding từ câu hỏi
def embedding_fn(text):
    return embeddings.embed_query(text)

_GLOBAL_STATE = {}

def get_global_state():
    return _GLOBAL_STATE

def set_global_state(key, value):
    _GLOBAL_STATE[key] = value
