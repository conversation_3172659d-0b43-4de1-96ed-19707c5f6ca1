from fastapi import <PERSON>AP<PERSON>
from app.routers import user, session
from fastapi.middleware.cors import CORSMiddleware
from app.config.index import settings
from app.core.database import close_db_connection
from app.core.cache_manager import initialize_cache_manager
import logging
import asyncio
from contextlib import asynccontextmanager

# Configure logging based on settings
log_level = getattr(logging, settings.LOG_LEVEL.upper())
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Tắt các connection logs
logging.getLogger("pymongo").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)
logger.info(f"Starting application in {settings.ENVIRONMENT} mode")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for startup and shutdown events"""
    # Startup
    try:
        logger.info("Starting up AI Engine...")

        # Initialize cache manager with settings
        global cache_manager
        cache_manager = initialize_cache_manager(settings)
        logger.info(f"Cache manager initialized with {settings.ENVIRONMENT} settings")

        # Pre-warm connections
        logger.info("Pre-warming database connections...")
        from app.core.database import get_db
        await get_db()

        logger.info("Pre-warming connections...")
        # Test simple connections
        from app.utils.qdrant_simple import health_check_qdrant
        from app.utils.elasticsearch_optimized import health_check_elasticsearch_simple

        try:
            is_qdrant_healthy = health_check_qdrant()
            if is_qdrant_healthy:
                logger.info("Qdrant client ready")
            else:
                logger.warning("Qdrant health check failed")
        except Exception as e:
            logger.warning(f"Qdrant client warning: {e}")

        try:
            # Test ES health instead of searching non-existent index
            is_healthy = health_check_elasticsearch_simple(settings.ES_URL)
            if is_healthy:
                logger.info("Elasticsearch client ready")
            else:
                logger.warning("Elasticsearch health check failed")
        except Exception as e:
            logger.warning(f"Elasticsearch client warning: {e}")

        # Start scheduler (commented out for now)
        # start_scheduler()
        # logger.info("Scheduler started")

        # Start cache cleanup task
        cleanup_task = asyncio.create_task(periodic_cache_cleanup())

        logger.info("AI Engine startup completed successfully!")

        yield  # Application is running

    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise

    # Shutdown
    try:
        logger.info("Shutting down AI Engine...")

        # Cancel cleanup task
        if 'cleanup_task' in locals():
            cleanup_task.cancel()

        # Close database connections
        await close_db_connection()

        # Close simple connections
        from app.utils.qdrant_simple import close_qdrant_connections
        close_qdrant_connections()

        logger.info("AI Engine shutdown completed")

    except Exception as e:
        logger.error(f"Shutdown error: {e}")


app = FastAPI(
    title="AI Engine API",
    description="Optimized AI chatbot with hybrid search capabilities",
    version="1.0.0",
    lifespan=lifespan
)


async def periodic_cache_cleanup():
    """Periodic cache cleanup task"""
    while True:
        try:
            await asyncio.sleep(3600)  # Run every hour
            results = await cache_manager.cleanup_all()
            logger.info(f"Cache cleanup completed: {results}")
        except Exception as e:
            logger.error(f"Cache cleanup error: {e}")


origins = [
    settings.ORIGIN_PORTAL,
    settings.ORIGIN_PORTAL_DOCKER,
    settings.ORIGIN_PORTAL_IP,
    settings.ORIGIN_KONG,
    settings.ORIGIN_KONG_IP,
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    from app.utils.qdrant_simple import health_check_qdrant
    from app.utils.elasticsearch_optimized import health_check_elasticsearch_simple

    qdrant_status = False
    es_status = False

    try:
        qdrant_status = health_check_qdrant()
    except Exception as e:
        logger.error(f"Qdrant health check error: {e}")

    try:
        es_status = health_check_elasticsearch_simple()
    except Exception as e:
        logger.error(f"ES health check error: {e}")

    return {
        "status": "healthy" if qdrant_status and es_status else "degraded",
        "services": {
            "qdrant": "healthy" if qdrant_status else "unhealthy",
            "elasticsearch": "healthy" if es_status else "unhealthy"
        },
        "cache_stats": cache_manager.get_all_stats()
    }

# Debug endpoint for ES data
@app.get("/debug/es/{query}")
async def debug_es_search(query: str):
    """Debug endpoint to test ES search"""
    from app.utils.elasticsearch_optimized import search_elasticsearch_simple

    try:
        results = search_elasticsearch_simple(query, settings.ES_INDEX, settings.ES_URL)
        return {
            "query": query,
            "index": settings.ES_INDEX,
            "url": settings.ES_URL,
            "results_count": len(results),
            "results": results[:3],  # First 3 results only
            "sample_structure": {
                "type": type(results[0]).__name__ if results else "No results",
                "keys": list(results[0].keys()) if results and isinstance(results[0], dict) else "N/A",
                "_source_keys": list(results[0].get('_source', {}).keys()) if results and isinstance(results[0], dict) and '_source' in results[0] else "N/A"
            }
        }
    except Exception as e:
        return {
            "error": str(e),
            "query": query,
            "index": settings.ES_INDEX,
            "url": settings.ES_URL
        }

app.include_router(user.router, prefix="/api/v1")
app.include_router(session.router, prefix="/api/v1")
