from langchain.agents import AgentType, initialize_agent
from langchain_ollama import OllamaLLM
from app.engine.react.tools.index import tools
from langchain.agents import AgentExecutor
from app.config.index import settings

llm = OllamaLLM(model="llama3:8b", base_url=settings.OLLAMA_URL)

summarize_answer_agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
    handle_parsing_errors=True,
)

def safe_run_agent(agent: AgentExecutor, query: str) -> str:
    try: 
        return agent.run(query)
    except Exception as e:
        if hasattr(e, "intermediate_steps"):
            steps = e.intermediate_steps
            for step in reversed(steps):
                log = getattr(step[1], "log", None)
                if log:
                    return f"Final Answer: Stopped due to limit. Latest result is: \n{log}"

        return "Final Answer: Unknown error occurred during reasoning."
